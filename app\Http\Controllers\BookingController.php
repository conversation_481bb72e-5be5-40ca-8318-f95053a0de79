<?php

namespace App\Http\Controllers;

use App\Models\Service;
use App\Models\Booking;
use App\Models\BookingService;
use App\Models\Staff;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\BookingsExport;
use App\Models\Friend;
use Stripe\Stripe;
use Stripe\Checkout\Session;

class BookingController extends Controller
{
    public function saveBookingDetails(Request $request)
    {
        $service = Service::where('ids', $request->service_id)->with('user')->firstOrFail();
        // If booking number is provided, update the existing booking in the database
        if ($request->booking_number != null) {
            $booking = Booking::where('booking_number', $request->booking_number)->firstOrFail();
            $booking->update([
                'user_id' => $request->user_id,
                'service_id' => $service->id,
                'booking_date' => $request->booking_date,
                'booking_time' => $request->booking_time,
                'comments' => $request->comments,
            ]);
            return response()->json([
                'status' => 'success',
                'booking' => $booking,
            ]);
        }

        // If no booking number is provided, store in session as before
        $bookingCard = [
            'service' => $service,
            'booking_date' => $request->booking_date,
            'booking_time' => $request->booking_time,
            'comments' => $request->comments,
            'loc' => $request->loc,
            'loc_type' => $request->loc_type,
            'lat' => $request->lat,
            'lng' => $request->lng,
            'address' => $request->address,
            'user_id' => $request->user_id,
            'category' => $request->category,
            'selected_professionals' => $request->selected_professionals ?? [],
        ];

        $bookingCards = session()->get('booking_cards', []);
        $updated = false;

        foreach ($bookingCards as $index => $card) {
            if (isset($card['service']) && $card['service']['ids'] == $request->service_id) {
                $bookingCards[$index] = $bookingCard; // Replace the existing entry
                $updated = true;
                break;
            }
        }

        if (!$updated) {
            $bookingCards[] = $bookingCard;
        }

        session(['booking_cards' => $bookingCards]);

        return response()->json([
            'status' => 'success',
            'booking_cards' => session('booking_cards'),
        ]);
    }

    public function checkout(Request $request)
    {
        Stripe::setApiKey(env('STRIPE_SECRET'));

        $session = Session::create([
            'payment_method_types' => ['card'],
            'line_items' => [[
                'price_data' => [
                    'currency' => 'usd',
                    'product_data' => [
                        'name' => 'Test Product',
                    ],
                    'unit_amount' => $request->total * 100, // $10.00
                ],
                'quantity' => 1,
            ]],
            'mode' => 'payment',
            'success_url' => route('success'),
            'cancel_url' => route('cancel'),
        ]);

        return redirect($session->url);
    }
    public function paymentSuccess(Request $request)
    {
        // try {
        //     DB::beginTransaction();
        $bookingCards = session()->get('booking_cards');
        if (!$bookingCards || !is_array($bookingCards)) {
            return response()->json(['message' => 'No booking data found in session.'], 400);
        }
        foreach ($bookingCards as $card) {

            $service = $card['service'];
            $user = $service['user'];
            $booking = new Booking();
            $booking->booking_number = 'BK-' . mt_rand(10000000, 99999999);
            $booking->service_id   = $service['id'];
            if ($card['category'] == 'family') {
                $friend = Friend::where('ids', $card['user_id'])->first();
                $booking->user_id = $friend->id;
            } else {
                $booking->user_id = $card['user_id'];
            }
            $booking->provider_id  = $user['id'];
            $booking->booking_date = $card['booking_date'];
            $booking->booking_time = $card['booking_time'];
            $booking->duration     = (int) $service['duration'];
            $booking->vat_amount   = null; // or set a calculated value
            $booking->service_price = (float) $service['price'];
            $booking->total_amount = (float) $service['price'];
            $booking->comments     = $card['comments'] ?? null;
            $booking->save();
            // Store selected professionals in booking_service pivot table
            if (isset($card['selected_professionals']) && !empty($card['selected_professionals'])) {
                Log::info('PaymentSuccess: Storing selected professionals', [
                    'booking_id' => $booking->id,
                    'selected_professionals' => $card['selected_professionals']
                ]);

                foreach ($card['selected_professionals'] as $selectedProfessional) {
                    // Get the staff member details
                    $staff = Staff::find($selectedProfessional['id']);

                    if ($staff) {
                        $bookingService = BookingService::create([
                            'booking_id' => $booking->id,
                            'service_id' => $service['id'],
                            'staff_id' => $staff->id,
                            'user_id' => $staff->user_id, // Professional's user account if linked
                        ]);

                        Log::info('PaymentSuccess: Created booking service record', [
                            'booking_service_id' => $bookingService->id,
                            'booking_id' => $booking->id,
                            'staff_id' => $staff->id,
                            'staff_name' => $staff->name,
                            'user_id' => $staff->user_id
                        ]);
                    } else {
                        Log::warning('PaymentSuccess: Staff member not found', [
                            'staff_id' => $selectedProfessional['id']
                        ]);
                    }
                }
            } else {
                Log::info('PaymentSuccess: No selected professionals found for booking', [
                    'booking_id' => $booking->id,
                    'card_keys' => array_keys($card)
                ]);
            }
        }
        // Optionally clear the session
        session()->forget('booking_cards');
        // DB::commit();
        return redirect('/')->with([
            'type' => 'success',
            'message' => 'Payment done successfully'
        ]);
        // } catch (\Exception $e) {
        //     DB::rollback();
        //     return redirect('/')->with([
        //         'type' => 'error',
        //         'message' => 'Failed to process payment: ' . $e->getMessage()
        //     ]);
        // }
    }
    public function paymentCancel()
    {
        return redirect('/')->with([
            'type' => 'error',
            'message' => 'Payment canceled.'
        ]);
    }

    public function customerBooking()
    {
        // upcomming booking
        $upcommingBookings = Booking::with("service")->where('user_id', auth()->id())->where('booking_date', '>=', Carbon::today())->get();
        // past booking
        $pastBookings = Booking::with("service")->where('user_id', auth()->id())->where('booking_date', '<', Carbon::today())->get();

        return view('dashboard.customer.customer-booking', compact('upcommingBookings', 'pastBookings'));
    }

    public function getCustomerBookingsForCalendar()
    {
        $bookings = Booking::with(['service', 'service.user'])
            ->where('user_id', auth()->id())
            ->get()
            ->map(function ($booking) {
                return [
                    'id' => $booking->id,
                    'title' => $booking->service->name,
                    'start' => $booking->booking_date . 'T' . $booking->booking_time,
                    'end' => date('Y-m-d\TH:i:s', strtotime($booking->booking_date . ' ' . $booking->booking_time . ' +' . $booking->duration . ' minutes')),
                    'backgroundColor' => '#006AA0',
                    'borderColor' => '#006AA0',
                    'textColor' => '#ffffff',
                    'extendedProps' => [
                        'booking_number' => $booking->booking_number,
                        'service_name' => $booking->service->name,
                        'service_price' => $booking->price ?? $booking->total_amount ?? 0,
                        'provider_name' => $booking->service->user->name ?? 'N/A',
                        'booking_time' => date('h:i A', strtotime($booking->booking_time)),
                        'duration' => $booking->duration,
                        'comments' => $booking->comments,
                        'status' => $booking->hasTimePassed() ? 'past' : 'upcoming'
                    ]
                ];
            });

        return response()->json($bookings);
    }
    public function dashboardBooking()
    {
        $bookings = Booking::query();
        $bookings->with(['service', 'service.user', 'customer']);

        if (auth()->user()->hasAnyRole(['individual', 'professional', 'business'])) {
            $bookings->where('provider_id', auth()->id());
        }

        $bookings = $bookings->get();

        // Calculate booking statistics
        $stats = $this->calculateBookingStats();

        return view('dashboard.business.business-booking', compact('bookings', 'stats'));
    }

    public function filterBookings(Request $request)
    {
        $query = Booking::query();
        $query->with(['service', 'service.user', 'customer']);

        // Apply role-based filtering
        if (auth()->user()->hasAnyRole(['individual', 'professional', 'business'])) {
            $query->where('provider_id', auth()->id());
        }

        // Apply search filter
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('booking_number', 'like', "%{$search}%")
                    ->orWhereHas('customer', function ($customerQuery) use ($search) {
                        $customerQuery->where('name', 'like', "%{$search}%");
                    })
                    ->orWhereHas('service', function ($serviceQuery) use ($search) {
                        $serviceQuery->where('name', 'like', "%{$search}%");
                    });
            });
        }

        // Apply status filter
        if ($request->filled('status') && $request->get('status') !== 'all') {
            $status = $request->get('status');
            switch ($status) {
                case 'ongoing':
                    $query->where('status', 0)->whereRaw("CONCAT(booking_date, ' ', booking_time) < ?", [now()]);
                    break;
                case 'upcoming':
                    $query->where('status', 0)->whereRaw("CONCAT(booking_date, ' ', booking_time) >= ?", [now()]);
                    break;
                case 'complete':
                    $query->where('status', 1);
                    break;
                case 'canceled':
                    $query->where('status', 2);
                    break;
            }
        }

        // Apply category filter
        if ($request->filled('category') && $request->get('category') !== 'Category' && $request->get('category') !== 'all') {
            $category = $request->get('category');
            $query->whereHas('service', function ($serviceQuery) use ($category) {
                $serviceQuery->whereHas('category', function ($catQuery) use ($category) {
                    $catQuery->where('name', 'like', "%{$category}%");
                });
            });
        }

        // Apply staff filter (for business users)
        if ($request->filled('staff') && $request->get('staff') !== 'Staff' && $request->get('staff') !== 'all' && auth()->user()->hasRole('business')) {
            $staff = $request->get('staff');
            $query->whereHas('bookingServices', function ($bookingServiceQuery) use ($staff) {
                $bookingServiceQuery->whereHas('staff', function ($staffQuery) use ($staff) {
                    $staffQuery->where('name', 'like', "%{$staff}%");
                });
            });
        }

        // Apply date filter
        if ($request->filled('date')) {
            $dateRange = $request->get('date');
            $dates = explode(' - ', $dateRange);
            if (count($dates) == 2) {
                $startDate = \Carbon\Carbon::createFromFormat('M d, Y', trim($dates[0]))->format('Y-m-d');
                $endDate = \Carbon\Carbon::createFromFormat('M d, Y', trim($dates[1]))->format('Y-m-d');
                $query->whereBetween('booking_date', [$startDate, $endDate]);
            } elseif (count($dates) == 1) {
                $date = \Carbon\Carbon::createFromFormat('M d, Y', trim($dates[0]))->format('Y-m-d');
                $query->whereDate('booking_date', $date);
            }
        }

        $bookings = $query->orderBy('booking_date', 'desc')->get();

        // Return JSON response for AJAX requests
        if ($request->ajax()) {
            $html = view('dashboard.business.partials.booking-table-rows', compact('bookings'))->render();
            return response()->json([
                'success' => true,
                'html' => $html,
                'count' => $bookings->count()
            ]);
        }

        $stats = $this->calculateBookingStats();
        return view('dashboard.business.business-booking', compact('bookings', 'stats'));
    }

    private function calculateBookingStats()
    {
        $baseQuery = Booking::query();

        // Apply role-based filtering for stats
        if (auth()->user()->hasAnyRole(['individual', 'professional', 'business'])) {
            $baseQuery->where('provider_id', auth()->id());
        }

        // Total bookings
        $totalBookings = (clone $baseQuery)->count();

        // Completed bookings (status 1)
        $completedBookings = (clone $baseQuery)->where('status', 1)->count();

        // Ongoing bookings (status 0 - pending bookings where time has passed)
        $ongoingBookings = (clone $baseQuery)
            ->where('status', 0)
            ->whereRaw("CONCAT(booking_date, ' ', booking_time) < ?", [now()])
            ->count();

        // Upcoming bookings (status 0 - pending bookings where time has not passed)
        $upcomingBookings = (clone $baseQuery)
            ->where('status', 0)
            ->whereRaw("CONCAT(booking_date, ' ', booking_time) >= ?", [now()])
            ->count();

        // Calculate percentage changes (mock data for now - you can implement actual historical comparison)
        return [
            'total_bookings' => $totalBookings,
            'total_bookings_change' => 17.2, // Mock percentage
            'active_bookings' => $completedBookings, // This is now "Completed Bookings" as per your UI change
            'active_bookings_change' => 17.2, // Mock percentage
            'ongoing_bookings' => $ongoingBookings,
            'ongoing_bookings_change' => 8.5, // Mock percentage
            'upcoming_bookings' => $upcomingBookings,
            'upcoming_bookings_change' => -2.3, // Mock percentage
        ];
    }

    public function updateBookingStatus(Request $request)
    {
        $request->validate([
            'booking_id' => 'required|exists:bookings,id',
            'action' => 'required|in:complete,cancel'
        ]);

        $booking = Booking::findOrFail($request->booking_id);

        // Check if user has permission to update this booking
        if (auth()->user()->hasAnyRole(['individual', 'professional', 'business']) && $booking->provider_id !== auth()->id()) {
            return response()->json(['success' => false, 'message' => 'Unauthorized'], 403);
        }

        // Update booking status
        if ($request->action === 'complete') {
            $booking->status = 1; // Completed
        } elseif ($request->action === 'cancel') {
            $booking->status = 2; // Cancelled
        }

        $booking->save();

        return response()->json([
            'success' => true,
            'message' => 'Booking status updated successfully'
        ]);
    }

    public function getBusinessBookingsForCalendar()
    {
        $query = Booking::with(['service', 'customer']);

        // Apply role-based filtering
        if (auth()->user()->hasAnyRole(['individual', 'professional', 'business'])) {
            $query->where('provider_id', auth()->id());
        }

        $bookings = $query->get()->map(function ($booking) {
            $backgroundColor = '#006AA0'; // Default blue
            $textColor = '#ffffff';

            // Set colors based on status and date
            switch ($booking->status) {
                case 0: // Pending/Active
                    if ($booking->hasTimePassed()) {
                        $backgroundColor = '#F59E0B'; // Orange for ongoing/past pending
                        $statusText = 'Ongoing';
                    } else {
                        $backgroundColor = '#3B82F6'; // Blue for upcoming
                        $statusText = 'Upcoming';
                    }
                    break;
                case 1: // Completed
                    $backgroundColor = '#10B981'; // Green
                    $statusText = 'Completed';
                    break;
                case 2: // Cancelled
                    $backgroundColor = '#EF4444'; // Red
                    $statusText = 'Cancelled';
                    break;
                default:
                    $statusText = 'Unknown';
            }

            // Create unique event ID to avoid duplication
            $eventId = 'booking_' . $booking->id;

            return [
                'id' => $eventId,
                'title' => $booking->service->name . ' - ' . ($booking->customer->name ?? 'N/A'),
                'start' => $booking->booking_date . 'T' . $booking->booking_time,
                'end' => date('Y-m-d\TH:i:s', strtotime($booking->booking_date . ' ' . $booking->booking_time . ' +' . ($booking->duration ?? 60) . ' minutes')),
                'backgroundColor' => $backgroundColor,
                'borderColor' => $backgroundColor,
                'textColor' => $textColor,
                'allDay' => false,
                'extendedProps' => [
                    'booking_id' => $booking->id,
                    'booking_number' => $booking->booking_number,
                    'service_name' => $booking->service->name,
                    'customer_name' => $booking->customer->name ?? 'N/A',
                    'service_price' => $booking->price ?? $booking->total_amount ?? 0,
                    'booking_date' => $booking->booking_date,
                    'booking_time' => date('h:i A', strtotime($booking->booking_time)),
                    'duration' => $booking->duration ?? 60,
                    'comments' => $booking->comments,
                    'status' => $booking->status,
                    'status_text' => $statusText
                ]
            ];
        });

        return response()->json($bookings->values()); // Use values() to ensure clean array indices
    }

    /**
     * Show booking detail page
     */
    public function showBookingDetail($ids, $booking_number)
    {
        try {
            $query = Booking::with([
                'service',
                'service.category',
                'service.user',
                'customer',
            ]);

            // Apply role-based filtering for security
            if (auth()->user()->hasAnyRole(['individual', 'professional', 'business'])) {
                $query->where('provider_id', auth()->id())->where('booking_number', $booking_number)->where('ids', $ids);
            }

            $booking = $query->firstOr();

            return view('dashboard.business.booking-detail', compact('booking'));
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return view('dashboard.business.booking-detail', [
                'error' => 'Booking not found or you do not have permission to view this booking.'
            ]);
        } catch (\Exception $e) {
            Log::error('Error showing booking details: ' . $e->getMessage());
            return view('dashboard.business.booking-detail', [
                'error' => 'An error occurred while loading booking details. Please try again.'
            ]);
        }
    }

    /**
     * Export bookings to CSV
     */
    public function exportBookings()
    {
        try {
            $query = Booking::query();
            $query->with(['service', 'service.category', 'customer']);

            // Apply role-based filtering (same as dashboard)
            $userRoles = auth()->user()->roles->pluck('name')->toArray();
            if (array_intersect($userRoles, ['individual', 'professional', 'business'])) {
                $query->where('provider_id', auth()->id());
            }

            $bookings = $query->orderBy('booking_date', 'desc')->get();

            // Generate filename with current date
            $filename = 'bookings_' . date('Y-m-d') . '.csv';

            return Excel::download(new BookingsExport($bookings), $filename);
        } catch (\Exception $e) {
            Log::error('Booking export error: ' . $e->getMessage());
            return redirect()->back()->with([
                'title' => 'Error',
                'message' => 'Failed to export bookings. Please try again.',
                'type' => 'error'
            ]);
        }
    }
}
