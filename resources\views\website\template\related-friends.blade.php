@if ($friends->count() > 0)
    <div class="service-details d-flex justify-content-between p-3 pb-5">
        <div class="service-name w-100">
            <label class="form-label form-input-labels pb-3">Select Friends</label>
            <div class="professional-checkbox row row-gap-5">
                {{-- @php
                $images = [
                    'family1.png',
                    'family2.png',
                    'family3.png',
                    'family4.png',
                    'family4.png',
                    'family4.png',
                    'family4.png',
                ];
            @endphp --}}
                @foreach ($friends as $friend)
                    <div class="col-md-4">
                        <label class="category-checkbox">
                            <input class="friends_ids" type="radio" name="friends" value="{{ $friend->ids }}" @if ($loop->first) checked @endif>
                            <div class="d-flex flex-column align-items-center">
                                <div
                                    class="card-header border-0 p-0 position-relative justify-content-center align-items-center">
                                    @if (isset($friend->profile->image))
                                        <img src="{{ asset('website/' . $friend->profile->image) }}"
                                            class="h-90px w-90px object-fit-contain rounded-pill top-rated-image"
                                            alt="card-image">
                                    @else
                                    <img src="{{ asset('website/assets/images/family1.png') }}"
                                            class="h-90px w-90px object-fit-contain rounded-pill top-rated-image"
                                            alt="card-image">
                                    @endif

                                </div>
                                <div class="card-body p-0">
                                    <p
                                        class="professional-name fs-16 semi_bold black text-center m-0 w-100 shadow-none ">
                                        {{ $friend->name }}</p>
                                    <p
                                        class="professional-profession fs-14 regular light-black opacity-6 text-center  m-0 w-100 shadow-none">
                                        {{ $friend->relationship }}</p>
                                </div>
                            </div>
                        </label>
                    </div>
                @endforeach
            </div>
        </div>
    </div>
@endif
