<?php

namespace App\Services;

use App\Models\Category;
use App\Models\Service;
use App\Models\SubCategory;
use Illuminate\Support\Str;
use App\Models\User;
use Illuminate\Support\Facades\DB;

class UserService
{
    public function getServices(?int $userId = null, string $type = 'all', int $perPage = 15, bool $withRelations = false)
    {
        $query = Service::query();

        // Eager load relationships if requested
        if ($withRelations) {
            $query->with(['category', 'subcategory', 'staff']);
        }

        // Apply user filter if user ID is provided
        if ($userId !== null) {
            $query->where('user_id', $userId);
        }

        // Apply type filter
        switch ($type) {
            case 'individual':
                $query->where('type', 'individual');
                break;
            case 'group':
                $query->where('type', 'group');
                break;
            case 'all':
            default:
                // No additional type filtering needed
                break;
        }

        return $query->paginate($perPage);
    }
    public function getGroupServices(int $userId)
    {
        return $this->getServices($userId, 'group');
    }

    public function getIndividualServices(int $userId)
    {
        return $this->getServices($userId, 'individual');
    }

    function individual_data($request)
    {
        $category = Category::where('ids', $request["category_id"])->first();
        if (!$category) {
            return false;
        }
        $subcategory = SubCategory::where('ids', $request["subcategory_id"])->first();
        if (!$subcategory) {
            return false;
        }
        $data = [
            'user_id' => auth()->id(),
            'type' => "individual",
            'name' => $request["name"],
            'slug' => Str::slug($request["name"]),
            'category_id' => $category->id,
            'subcategory_id' => $subcategory->id,
            'duration' => $request["duration"],
            'price' => $request["price"],
            'additional_cost' => $request["additional_cost"],
            'required_items' => $request["required_items"],
            'description' => $request["description"],
            'is_onsite' => $request['is_onsite'],
            'is_customer_location' => $request['is_customer_location'],
            'lat' => $request['lat'],
            'lng' => $request['lng'],
        ];

        // Handle image upload
        if (isset($request['thumbnail'])) {
            $data['image'] = storeImage('service-images', $request['thumbnail']);
        }

        // Conditional fields based on service type
        if ($data['is_onsite']) {
            $data['physical_location'] = $request["physical_location"];
        }

        if ($data['is_customer_location']) {
            $data['radius'] = $request["radius"];
            $data['travel_time'] = $request["travel_time"];
            $data['service_charges'] = $request["service_charges"];
        }
        return $data;
    }
    function group_data($request)
    {
        $category = Category::where('ids', $request["category_id"])->first();
        if (!$category) {
            return false;
        }
        $subcategory = SubCategory::where('ids', $request["subcategory_id"])->first();
        if (!$subcategory) {
            return false;
        }
        // Determine which coordinates to use based on service location
        $lat = null;
        $lng = null;
        if ($request["service_location"] == "onsite") {
            $lat = $request["lat"] ?? null;
            $lng = $request["lng"] ?? null;
        } elseif ($request["service_location"] == "outside-location") {
            $lat = $request["outside_lat"] ?? null;
            $lng = $request["outside_lng"] ?? null;
        }

        $data = [
            'user_id' => auth()->id(),
            'type' => "group",
            'name' => $request["name"],
            'slug' => Str::slug($request["name"]),
            'category_id' => $category->id,
            'subcategory_id' => $subcategory->id,
            'duration' => $request["duration"],
            'discount_recurring' => $request["discount_recurring"],
            'total_slots' => $request["total_slots"],
            'price' => $request["price_per_slot"],
            'additional_cost' => $request["additional_cost"],
            'additional_service_tax' => $request["additional_service_tax"],
            'required_items' => $request["required_items"],
            'description' => $request["description"],
            'lat' => $lat,
            'lng' => $lng,
            // service location
            "is_onsite" => $request["service_location"] == "onsite" ? 1 : 0,
            "outside_location" => $request["service_location"] == "outside-location" ? $request["outside_location"] ?? null : null,
            "physical_location" => $request["service_location"] == "onsite" ? $request["physical_location"] ?? null : null,
        ];

        // checking service location type here
        if($request["service_location"] == "outside-location"){
            unset($data['physical_location']);
        }elseif($request["service_location"] == "onsite"){
            unset($data['outside_location']);
        }

        // Handle image upload
        if (isset($request['thumbnail'])) {
            $data['image'] = storeImage('service-images', $request['thumbnail']);
        }

        return $data;
    }
    function createService($request_data, $type)
    {
        DB::beginTransaction();
        try {
            $data = [];
            if ($type == 'individual') {
                $data = $this->individual_data($request_data);
            } elseif ($type == 'group') {
                $data = $this->group_data($request_data);
            }
            if (empty($data)) {
                return false;
            }
            $service = Service::create($data);

            // Assign staff to service if provided
            if ($request_data->has('staff_ids') && is_array($request_data->staff_ids)) {
                $service->staff()->sync($request_data->staff_ids);
            }

            $createAvailabilities = $this->createAvailabilities(service: $service, availabilities: json_decode($request_data->availabilities_dates, true));
            if (!$createAvailabilities) {
                return false;
            }
            DB::commit();
            return $service;
        } catch (\Throwable $th) {
            DB::rollback();
            return $th->getMessage();
        }
    }
    function updateService($request_data, $service_ids, $type)
    {
        DB::beginTransaction();
        try {
            $service = Service::where('ids', $service_ids)->firstOrFail();
            $data = [];
            if ($type == 'individual') {
                $data = $this->individual_data($request_data);
            } elseif ($type == 'group') {
                $data = $this->group_data($request_data);
            }
            if (empty($data)) {
                return false;
            }
            $service->update($data);

            // Update staff assignments
            if ($request_data->has('staff_ids') && is_array($request_data->staff_ids)) {
                $service->staff()->sync($request_data->staff_ids);
            } else {
                // If no staff selected, remove all assignments
                $service->staff()->detach();
            }

            $createAvailabilities = $this->createAvailabilities(service: $service, availabilities: json_decode($request_data->availabilities_dates, true));
            if (!$createAvailabilities) {
                return false;
            }
            DB::commit();
            return $service;
        } catch (\Throwable $th) {
            DB::rollback();
            return false;
        }
    }
    function createAvailabilities(Service $service, $availabilities)
    {
        DB::beginTransaction();
        try {
            $service->availabilities()->delete();

            // Handle empty or null availabilities
            if (empty($availabilities) || !is_array($availabilities)) {
                DB::commit();
                return true;
            }

            foreach ($availabilities as $availability) {
                // Skip if availability data is incomplete
                if (!isset($availability['start']) || !isset($availability['end']) || !isset($availability['date']) || !isset($availability['day'])) {
                    continue;
                }

                $availability['start_time'] = date('H:i:s', strtotime($availability['start']));
                $availability['end_time'] = date('H:i:s', strtotime($availability['end']));
                unset($availability['start']);
                unset($availability['end']);
                $availability['service_id'] = $service->id;
                $availability['day'] = $availability['day'];
                $availability['date'] = $availability['date'];
                $service->availabilities()->create($availability);
            }
            DB::commit();
            return true;
        } catch (\Throwable $th) {
            DB::rollback();
            return false;
        }
    }

    function getSingleService($service_ids)
    {
        return Service::with("category.subcategories", "availabilities", "staff")->where('ids', $service_ids)->firstOrFail();
    }
    function deleteService($service_ids)
    {
        try {
            DB::beginTransaction();
            $service = $this->getSingleService(service_ids: $service_ids);
            if (!$service) {
                return false;
            }
            if ($service->image) {
                deleteImage($service->image);
            }
            $service->delete();
            DB::commit();
            return true;
        } catch (\Throwable $th) {
            DB::rollback();
            return false;
        }
    }
}
