@extends('dashboard.layout.master')

@section('content')
    <div id="kt_app_content" class="app-content flex-column-fluid booking">
        <div id="kt_app_content_container" class="app-container container padding-block">
            <div class="row">
                <div class="col-md-12">
                    <h6 class="semi_bold sora black">Earnings</h6>
                    <p class="fs-14 normal sora light-black">Lorem ipsum dolor sit amet consectetur. </p>
                </div>
            </div>
            <div class="row row-gap-5 mb-10 card-wrapper">
                <div class="col-xl-6 col-lg-6 col-sm-6 col-12">
                    <a href="javascript:void(0)">
                        <div class=" card-box d-flex flex-row gap-3 align-items-center">
                            <div class="card-header">
                                <div class="icon_cards bg-deep-blue">
                                    @include('svg.dollar')
                                </div>
                            </div>
                            <div class="card-body w-50">
                                <p class="fs-14 opacity-6 light-black m-0">
                                    Total Earnings
                                </p>
                                <p class="fs-24 semi_bold sora dark-black m-0" data-kt-countup="true"
                                    data-kt-countup-value="{{ $earningsStats['total_earnings'] ?? 0 }}" data-kt-countup-prefix="$">
                                </p>
                            </div>
                            <div class="card-footer ">
                                <div class="fs-12 w-700 {{ $earningsStats['total_earnings_change'] >= 0 ? 'green-box green' : 'red-box red' }}">
                                    <i class="fa-solid fa-arrow-{{ $earningsStats['total_earnings_change'] >= 0 ? 'up analytics-green-arrow' : 'down analytics-red-arrow' }}"></i>
                                    {{ abs($earningsStats['total_earnings_change']) }}%
                                </div>
                            </div>
                        </div>
                    </a>
                </div>
                <div class="col-xl-6 col-lg-6 col-sm-6 col-12">
                    <a href="javascript:void(0)">
                        <div class=" card-box d-flex flex-row gap-3 align-items-center">
                            <div class="card-header">
                                <div class="icon_cards bg-light-blue">
                                    @include('svg.earning')
                                </div>
                            </div>
                            <div class="card-body w-50 ">
                                <p class="fs-14 opacity-6 light-black m-0">
                                    Payment Being Cleared
                                </p>
                                <p class="fs-24 semi_bold sora dark-black m-0" data-kt-countup="true"
                                    data-kt-countup-value="{{ $earningsStats['payment_being_cleared'] ?? 0 }}" data-kt-countup-prefix="$">
                                </p>
                            </div>
                            <div class="card-footer">
                                <div class="fs-12 w-700 {{ $earningsStats['payment_being_cleared_change'] >= 0 ? 'green-box green' : 'red-box red' }}">
                                    <i class="fa-solid fa-arrow-{{ $earningsStats['payment_being_cleared_change'] >= 0 ? 'up analytics-green-arrow' : 'down analytics-red-arrow' }}"></i>
                                    {{ abs($earningsStats['payment_being_cleared_change']) }}%
                                </div>
                            </div>
                        </div>
                    </a>
                </div>
            </div>
            <div class="row">
                <div class="col-lg-12">
                    <div class="table-container">
                        <div class="table_top d-flex gap-3 align-items-center flex-wrap">
                            <div class="search_box">
                                <label for="customSearchInput">
                                    <i class="fas fa-search"></i>
                                </label>
                                <input class="search_input search" type="text" id="customSearchInput"
                                    placeholder="Search..." />
                            </div>
                            <!-- Select with dots -->
                            <div class="dropdown search_box select-box">
                                <button
                                    class="dropdown-toggle search_input status-dropdown-button d-flex align-items-center justify-content-start"
                                    type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <span><span class="dot"></span>
                                        All</span>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item dropdown-status" href="#" data-label="All"
                                            data-color="#4B5563"><span class="dot all"></span>
                                            All</a></li>
                                    <li><a class="dropdown-item dropdown-status" href="#" data-label="Ongoing"
                                            data-color="#F59E0B"><span class="dot ongoing"></span>
                                            Ongoing</a></li>
                                    <li><a class="dropdown-item dropdown-status" href="#" data-label="Upcoming"
                                            data-color="#3B82F6"><span class="dot upcoming"></span>
                                            Upcoming</a></li>
                                    <li><a class="dropdown-item dropdown-status" href="#" data-label="Complete"
                                            data-color="#10B981"><span class="dot completed"></span>
                                            Complete</a></li>
                                    <li><a class="dropdown-item dropdown-status" href="#" data-label="Canceled"
                                            data-color="#EF4444"><span class="dot cancelled-dot"></span>
                                            Canceled</a></li>
                                </ul>
                            </div>

                            <!-- category -->
                            <div class="search_box select-box">
                                <select class="search_input" id="categoryFilter">
                                    <option value="Category">Category</option>
                                    <option value="Category">All</option>
                                    @foreach($categories as $category)
                                        <option value="{{ $category->name }}">{{ $category->name }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <!-- Date Picker -->

                            <label for="datePicker" class="date_picker">
                                <div class="date-picker-container">
                                    <i class="bi bi-calendar-event calender-icon"></i>
                                    <input type="text" name="datePicker" class="datePicker w-200px ms-3">
                                    <i class="fa fa-chevron-down down-arrow ms-9"></i>

                                </div>
                            </label>
                            <div class="search_box d-block ms-auto">
                                <a href="#!" id="exportEarningsBtn" class="search_input fs-14 normal link-gray ">
                                    Export <i class="bi bi-file-arrow-down ms-1 file-icon"></i>
                                </a>
                            </div>

                        </div>
                        <table id="responsiveTable" class="responsiveTable display nowrap w-100">
                            <thead>
                                <tr>
                                    <th>Customer Name</th>
                                    <th>Service Type</th>
                                    <th>Client Name</th>
                                    <th>Amount</th>
                                    <th>Status</th>
                                    <th>Date</th>
                                    <th></th>
                                </tr>
                            </thead>
                            <tbody id="earningTableBody">
                                @include('dashboard.business.partials.earning-table-rows', ['bookings' => $bookings])
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('js')
<script>
    $(document).ready(function() {
        let searchTimeout;
        let currentStatus = 'all';
        let currentCategory = 'Category';

        // Search input with debouncing
        $('#customSearchInput').on('keyup', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(function() {
                performSearch();
            }, 300);
        });

        // Status dropdown handling
        $('.dropdown-status').on('click', function(e) {
            e.preventDefault();
            const label = $(this).data('label');
            const color = $(this).data('color');

            // Update button text and color
            const button = $(this).closest('.dropdown').find('.status-dropdown-button span');
            button.html(`<span class="dot" style="background-color: ${color}"></span> ${label}`);

            // Set current status
            currentStatus = label.toLowerCase();
            performSearch();
        });

        // Category dropdown handling
        $('#categoryFilter').on('change', function() {
            currentCategory = $(this).val();
            performSearch();
        });

        function performSearch() {
            const searchQuery = $('#customSearchInput').val();

            // Show loading state
            $('#earningTableBody').html('<tr><td colspan="7" class="text-center">Loading...</td></tr>');

            $.ajax({
                url: '{{ route('earning.filter') }}',
                type: 'GET',
                data: {
                    search: searchQuery,
                    status: currentStatus,
                    category: currentCategory
                },
                success: function(response) {
                    if (response.success) {
                        $('#earningTableBody').html(response.html);

                        // Show result count
                        if (searchQuery || currentStatus !== 'all' || currentCategory !== 'Category') {
                            console.log(`Found ${response.count} earning(s)`);
                        }
                    } else {
                        $('#earningTableBody').html(
                            '<tr><td colspan="7" class="text-center">Error loading earnings</td></tr>'
                            );
                    }
                },
                error: function() {
                    $('#earningTableBody').html(
                        '<tr><td colspan="7" class="text-center">Error loading earnings</td></tr>'
                        );
                }
            });
        }

        // Handle export button click
        $(document).on('click', '#exportEarningsBtn', function(e) {
            e.preventDefault();

            // Show loading state
            const originalText = $(this).html();
            $(this).html('<span class="spinner-border spinner-border-sm me-2" role="status"></span>Exporting...');

            // Build export URL with current filters
            const searchQuery = $('#customSearchInput').val();
            let exportUrl = '{{ route("earning.export") }}';
            let params = [];

            if (searchQuery) {
                params.push('search=' + encodeURIComponent(searchQuery));
            }
            if (currentStatus && currentStatus !== 'all') {
                params.push('status=' + encodeURIComponent(currentStatus));
            }
            if (currentCategory && currentCategory !== 'Category') {
                params.push('category=' + encodeURIComponent(currentCategory));
            }

            if (params.length > 0) {
                exportUrl += '?' + params.join('&');
            }

            // Trigger download with filters
            window.location.href = exportUrl;

            // Reset button after a delay
            setTimeout(function() {
                $('#exportEarningsBtn').html(originalText);
            }, 2000);
        });

        // Booking action handlers (Complete/Cancel)
        $(document).on('click', '.booking-action', function(e) {
            e.preventDefault();
            const bookingId = $(this).data('booking-id');
            const action = $(this).data('action');

            // Show SweetAlert confirmation
            const actionText = action === 'complete' ? 'complete' : 'cancel';
            const actionColor = action === 'complete' ? '#10B981' : '#EF4444';
            const actionIcon = action === 'complete' ? 'success' : 'warning';

            Swal.fire({
                title: `${actionText.charAt(0).toUpperCase() + actionText.slice(1)} Booking?`,
                text: `Are you sure you want to ${actionText} this booking?`,
                icon: actionIcon,
                showCancelButton: true,
                confirmButtonColor: actionColor,
                cancelButtonColor: '#6B7280',
                confirmButtonText: `Yes, ${actionText} it!`,
                cancelButtonText: 'Cancel'
            }).then((result) => {
                if (result.isConfirmed) {
                    updateBookingStatus(bookingId, action);
                }
            });
        });

        function updateBookingStatus(bookingId, action) {
            // Show loading
            Swal.fire({
                title: 'Processing...',
                text: 'Updating booking status',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            $.ajax({
                url: '{{ route('booking.update-status') }}',
                type: 'POST',
                data: {
                    _token: '{{ csrf_token() }}',
                    booking_id: bookingId,
                    action: action
                },
                success: function(response) {
                    if (response.success) {
                        // Refresh the table
                        performSearch();

                        // Show success message
                        const actionText = action === 'complete' ? 'completed' : 'cancelled';
                        Swal.fire({
                            title: 'Success!',
                            text: `Booking ${actionText} successfully!`,
                            icon: 'success',
                            confirmButtonColor: '#006AA0',
                            timer: 2000,
                            timerProgressBar: true,
                            willClose: () => {
                                // Reload the page to update the statistics cards
                                window.location.reload();
                            }
                        });
                    } else {
                        Swal.fire({
                            title: 'Error!',
                            text: 'Failed to update booking status',
                            icon: 'error',
                            confirmButtonColor: '#EF4444'
                        });
                    }
                },
                error: function() {
                    Swal.fire({
                        title: 'Error!',
                        text: 'Network error occurred while updating booking status',
                        icon: 'error',
                        confirmButtonColor: '#EF4444'
                    });
                }
            });
        }
    });
</script>
@endpush
