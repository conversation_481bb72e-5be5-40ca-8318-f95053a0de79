<div class="col-md-12">
    <?php if(!isset($service)): ?>
        <ul class="nav nav-pills mb-10" id="pills-tab" role="tablist">
            <li class="nav-item" role="presentation">
                <a href="<?php echo e(route('services.create', ["type" => "individual"])); ?>"
                    class="nav-link <?php echo e($type == 'individual' ? 'active' : ''); ?>  business-services"><?php echo $__env->make('svg.individula', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    Individual Services
                </a>
            </li>
            <li class="nav-item" role="presentation">
                <a class="nav-link business-services <?php echo e($type == 'group' ? 'active' : ''); ?>"
                    href="<?php echo e(route('services.create', ["type" => "group"])); ?>">
                    <?php echo $__env->make('svg.group', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    Group Service
                </a>
            </li>
        </ul>
    <?php endif; ?>
    <div>
        <?php echo $__env->renderWhen($type == 'individual', 'dashboard.service.include.individual', ["btn_text" => $btn_text ?? "Add"], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path'])); ?>
        <?php echo $__env->renderWhen($type == 'group', 'dashboard.service.include.group', ["btn_text" => $btn_text ?? "Add"], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path'])); ?>
    </div>

</div>

<?php $__env->startPush("js"); ?>
    <script>

        $('#category').on('select2:select', function (e) {
            $(this).select2('close');
        });

        $(document).ready(function () {
            // Initialize Select2 on page load
            $('#subcategory').select2();

            // Check if there's an old category value (for validation errors)
            var oldCategoryId = '<?php echo e(old("category_id")); ?>';
            var oldSubcategoryId = '<?php echo e(old("subcategory_id")); ?>';

            if (oldCategoryId && oldCategoryId !== '') {
                // Load subcategories for the old category value
                loadSubcategories(oldCategoryId, oldSubcategoryId);
            }

            $(document).on('change', '#category', function () {
                let category_id = $(this).val();
                loadSubcategories(category_id);
            });

            function loadSubcategories(category_id, selectedSubcategoryId = '') {
                let subcategory = $('#subcategory');

                if (category_id != '') {
                    $.ajax({
                        url: `<?php echo e(route('services.get-subcategories', ':category_id')); ?>`.replace(':category_id', category_id),
                        type: 'GET',
                        success: function (response) {
                            if (response.success && response.subcategories) {
                                // Clear existing options
                                subcategory.empty();
                                subcategory.append('<option value="">Select Subcategory</option>');

                                // Add new options
                                response.subcategories.forEach(element => {
                                    var isSelected = selectedSubcategoryId && selectedSubcategoryId == element.ids ? 'selected' : '';
                                    subcategory.append(`<option value="${element.ids}" ${isSelected}>${element.name}</option>`);
                                });

                                // Trigger change to update Select2
                                subcategory.trigger('change');
                            }
                        },
                        error: function (xhr, status, error) {
                            console.error('Error fetching subcategories:', error);
                            alert('An error occurred while fetching subcategories');
                        }
                    });
                } else {
                    // Clear subcategory when no category is selected
                    subcategory.empty();
                    subcategory.append('<option value="">Select Subcategory</option>');
                    subcategory.trigger('change');
                }
            }
        });
    </script>

    <!-- Google Maps API -->
    <script
        src="https://maps.googleapis.com/maps/api/js?key=AIzaSyA1Qkj7ocEmNs4U5-T2vVJ04OrsCRH0tCU&libraries=places&v=weekly"
        async defer></script>
<?php $__env->stopPush(); ?><?php /**PATH D:\SALMAN\git\anders\resources\views/dashboard/service/form.blade.php ENDPATH**/ ?>