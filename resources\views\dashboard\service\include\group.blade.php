<div>
    <form
        action="{{ isset($service) ? route('services.update', ['service' => $service->ids, 'type' => 'group']) : route('services.store', ['type' => 'group']) }}"
        method="POST" enctype="multipart/form-data" class="form-add-services" id="groupServiceForm">
        @csrf
        @isset($service)
            @method('PUT')
        @endisset
        <div class="row row-gap-5">
            {{-- Service Name --}}
            <div class="col-md-12">
                <label for="service-name" class="form-label form-input-labels">
                    Service Name<span class="text-danger">*</span>
                </label>
                <input type="text" class="form-control form-inputs-field" placeholder="Enter Service name"
                    id="service-name" name="name" value="{{ old('name', $service->name ?? '') }}">
                @error('name')
                    <p class="text-danger">
                        {{ $message }}
                    </p>
                @enderror
            </div>
            {{-- Service Name End --}}


            {{-- Category Name --}}
            <div class="col-md-6">
                <label for="category" class="form-label form-input-labels">Category<span
                        class="text-danger">*</span></label>
                <select class="form-select form-select-field" data-control="select2" data-dropdown-css-class="w-619px"
                    data-close-on-select="false" data-placeholder="Select an option" id="category" name="category_id">
                    <option></option>
                    @forelse ($categories as $category)
                        <option value="{{ $category->ids }}"
                            {{ old('category_id', $service->category->ids ?? '') == $category->ids ? 'selected' : '' }}>
                            {{ $category->name }}</option>
                    @empty
                        <option value="">No categories found</option>
                    @endforelse
                </select>
                @error('category_id')
                    <p class="text-danger">
                        {{ $message }}
                    </p>
                @enderror

                <label id="category-error" class="error" for="category"></label>
            </div>
            {{-- Category Name End --}}

            {{-- Subcategory --}}
            <div class="col-md-6">
                <label for="subcategory" class="form-label form-input-labels">Sub
                    Category<span class="text-danger">*</span></label>
                <select class="form-select form-select-field" data-control="select2" data-dropdown-css-class="w-619px"
                    data-close-on-select="false" data-placeholder="Select an option" data-allow-clear="true"
                    id="subcategory" name="subcategory_id">
                    <option value="">Select Subcategory</option>
                    @if (isset($service) && $service->category)
                        @forelse ($service->category->subcategories as $subcategory)
                            <option value="{{ $subcategory->ids }}"
                                {{ old('subcategory_id', $service->subcategory->ids ?? '') == $subcategory->ids ? 'selected' : '' }}>
                                {{ $subcategory->name }}</option>
                        @empty
                        @endforelse
                    @endif
                </select>
                @error('subcategory_id')
                    <p class="text-danger">
                        {{ $message }}
                    </p>
                @enderror
                <label id="subcategory-error" class="error" for="subcategory"></label>
            </div>
            {{-- Subcategory End --}}

            @if (auth()->check() && auth()->user()->hasRole('business'))
                <div class="col-md-12">
                    <label for="staff-member-secondary" class="form-label form-input-labels">Assign Staff
                        Members<span class="text-danger">*</span></label>
                    <select class="form-select form-select-field" data-control="select2"
                        data-dropdown-css-class="w-619px" data-close-on-select="false"
                        data-placeholder="Select staff members" data-allow-clear="true" multiple="multiple"
                        id="staff-member-secondary" name="staff_ids[]">
                        @forelse ($staffs as $staff)
                            <option value="{{ $staff->id }}"
                                {{ isset($service) && $service->staff->contains('id', $staff->id) ? 'selected' : '' }}>
                                {{ $staff->name }}</option>
                        @empty
                            <option disabled>No staff members available</option>
                        @endforelse
                    </select>
                    @error('staff_ids')
                        <p class="text-danger">
                            {{ $message }}
                        </p>
                    @enderror
                    <label id="staff-member-secondary-error" class="error" for="staff-member-secondary"></label>
                </div>

            @endif

            {{-- Availability --}}
            <div class="col-md-12">
                <label class="form-label form-input-labels">Availability<span class="text-danger">*</span></label>
                <x-service-availability-component :availabilities="$service->availabilities ?? []" />
                {{-- Hidden field for availability validation --}}
                <input type="hidden" name="availability_check" id="availability_check" value="">
                {{-- Error container for availability --}}
                <div id="availability-error-container"></div>
            </div>
            {{-- Availability End --}}

            {{-- Service Duration --}}
            <div class="col-md-6">
                <label for="duration" class="form-label form-input-labels">Service Duration<span
                        class="text-danger">*</span></label>
                <select name="duration" id="duration" class="form-control text-muted form-select-field">
                    <option value="">Select Service Duration</option>
                    <option value="15" {{ old('duration', $service->duration ?? '') == '15' ? 'selected' : '' }}>15
                        min
                    </option>
                    <option value="30" {{ old('duration', $service->duration ?? '') == '30' ? 'selected' : '' }}>30
                        min
                    </option>
                    <option value="45" {{ old('duration', $service->duration ?? '') == '45' ? 'selected' : '' }}>40
                        min
                    </option>
                    <option value="60" {{ old('duration', $service->duration ?? '') == '60' ? 'selected' : '' }}>60
                        min
                    </option>
                </select>
                @error('duration')
                    <p class="text-danger">
                        {{ $message }}
                    </p>
                @enderror
            </div>
            {{-- Service Duration End --}}



            {{-- <div class="col-md-6">
                <div class="d-flex  justify-content-between">
                    <label for="serviceTime" class="form-label form-input-labels">Service Day(s) &
                        Time</label>
                    <label class="styled-checkbox d-flex gap-3">
                        <input type="checkbox" name="recurring" id="recurring" checked>
                        <span class="fs-14 light-black normal">Recurring</span>
                    </label>
                </div>
                <div class="input-icon-wrapper">
                    <input type="text" id="serviceTime" class="form-control" readonly
                        placeholder="Select Service Day(s) & Time" />
                </div>
            </div> --}}
            {{-- <div class="col-md-6">
                <label for="recurring-duration" class="form-label form-input-labels">Recurring
                    Duration</label>
                <select class="form-select form-select-field" id="recurring-duration" name="recurring-duration"
                    data-control="select2" data-placeholder="Select">
                    <option></option>
                    <option value="1">1</option>
                    <option value="2">2</option>
                </select>
            </div> --}}
            <div class="col-md-6">
                <label for="discount_recurring" class="form-label form-input-labels">Discount For Recurring
                    <span class="normal opacity-6 light-black">(For more than 1 slot)<span
                            class="text-danger">*</span></span></label>
                <div class="input-group">
                    <span class="input-group-text" id="basic-addon1">%</span>
                    <input type="text" class="form-control form-inputs-field m-0" placeholder="Enter discount %"
                        id="discount_recurring" name="discount_recurring"
                        value="{{ old('discount_recurring', $service->discount_recurring ?? '') }}" />
                </div>
                @error('discount_recurring')
                    <p class="text-danger">
                        {{ $message }}
                    </p>
                @enderror
                <label id="discount_recurring-error" class="error" for="discount_recurring"></label>
            </div>

            {{-- Total Slots --}}
            <div class="col-md-6">
                <label for="total-slots" class="form-label form-input-labels">Total Slots<span
                        class="text-danger">*</span></label>
                <input type="number" min="1" class="form-control form-inputs-field"
                    value="{{ old('total_slots', $service->total_slots ?? '') }}" placeholder="Enter total slots"
                    id="total-slots" name="total_slots">
                @error('total_slots')
                    <p class="text-danger">
                        {{ $message }}
                    </p>
                @enderror
            </div>
            {{-- Total Slots End --}}

            {{-- Price Per Slot --}}
            <div class="col-md-3">
                <label for="price-per-slot" class="form-label form-input-labels">Price Per Slot
                    <span class="normal opacity-6 light-black">(Inclusive VAT)<span
                            class="text-danger">*</span></span></label>
                <input type="number" class="form-control form-inputs-field" placeholder="Enter price"
                    value="{{ old('price_per_slot', $service->price ?? '') }}" id="price-per-slot"
                    name="price_per_slot">
                @error('price_per_slot')
                    <p class="text-danger">
                        {{ $message }}
                    </p>
                @enderror
            </div>
            {{-- Price Per Slot End --}}

            {{-- Additional Costs --}}
            <div class="col-md-3">
                <label for="additional-costs" class="form-label form-input-labels">Additional
                    Costs<span class="text-danger">*</span></label>
                <input type="number" class="form-control form-inputs-field"
                    value="{{ old('additional_cost', $service->additional_cost ?? '') }}"
                    placeholder="Enter additional  costs" id="additional-costs" name="additional_cost">
                @error('additional-costs')
                    <p class="text-danger">
                        {{ $message }}
                    </p>
                @enderror
            </div>
            {{-- Additional Costs End --}}

            {{-- Required Items --}}
            <div class="col-md-12">
                <label for="required-items" class="form-label form-input-labels">Required
                    Items For Service<span class="text-danger">*</span></label>
                <input type="text" class="form-control form-inputs-field"
                    placeholder="Enter required items (separate with comma)" id="required-items"
                    name="required_items" value="{{ old('required_items', $service->required_items ?? '') }}">
                @error('required_items')
                    <p class="text-danger">
                        {{ $message }}
                    </p>
                @enderror
            </div>
            {{-- Required Items End --}}

            {{-- Description --}}
            <div class="col-md-12">
                <label for="description" class="form-label form-input-labels">Description<span
                        class="text-danger">*</span></label>
                <textarea class="form-control form-inputs-field form-textarea-field" id="description" name="description"
                    rows="4" placeholder="Enter Description here">{{ old('description', $service->description ?? '') }}</textarea>
                @error('description')
                    <p class="text-danger">
                        {{ $message }}
                    </p>
                @enderror
            </div>
            {{-- Description End --}}

            {{-- Service Location --}}
            <div class="col-md-4 d-flex gap-4">
                <label class="d-flex gap-2 align-items-center">
                    <input class="form-check-input" type="radio" name="service_location" value="onsite"
                        id="onsite-radio" @checked(old('service_location', $service->is_onsite ?? false) == 'onsite' ||
                                old('service_location', $service->is_onsite ?? false) == true)>
                    <span>On-site</span>
                </label>
                <label class="d-flex gap-2 align-items-center">
                    <input class="form-check-input" type="radio" name="service_location" value="outside-location"
                        id="outside-location-radio" @checked(old('service_location', $service->outside_location ?? false) == 'outside-location' ||
                                (old('service_location') == null && ($service->outside_location ?? false) == true))>
                    <span>Outside Location</span>
                </label>
            </div>
            @error('service_location')
                <p class="text-danger">
                    {{ $message }}
                </p>
            @enderror
            {{-- Service Location End --}}

            {{-- Physical & Outside Location --}}
            <div class="row row-gap-5">
                <!-- Physical Location -->
                <div class="col-md-12 form-hide-box" id="physical-location-seconday-field">
                    <label for="physical-location-secondary" class="form-label form-input-labels">Physical
                        Location</label>
                    <input type="text" class="form-control form-inputs-field form-textarea-field" id="pac-input"
                        name="physical_location" placeholder="Your registered business location"
                        value="{{ old('physical_location') }}">
                    <input type="hidden" name="lat" value="{{ old('lat', (isset($service) && $service->is_onsite) ? $service->lat : '') }}" id="latitude">
                    <input type="hidden" name="lng" value="{{ old('lng', (isset($service) && $service->is_onsite) ? $service->lng : '') }}" id="longitude">
                    <div class="custom_loc mt-2">
                        <div id="map" style="height: 300px"></div>
                    </div>
                </div>

                <!-- Outside Location -->
                <div class="col-md-12 form-hide-box" id="outside-location-field">
                    <label for="outside-location-secondary" class="form-label form-input-labels">Outside
                        Location</label>
                    <input type="text" class="form-control form-inputs-field form-textarea-field"
                        id="outside-location-input" name="outside_location"
                        placeholder="Please enter your location"
                        value="{{ old('outside_location', $service->outside_location ?? '') }}">
                    <input type="hidden" name="outside_lat" value="{{ old('outside_lat', (isset($service) && $service->outside_location) ? $service->lat : '') }}" id="outside-latitude">
                    <input type="hidden" name="outside_lng" value="{{ old('outside_lng', (isset($service) && $service->outside_location) ? $service->lng : '') }}" id="outside-longitude">
                    <div class="custom_loc mt-2">
                        <div id="outside-map" style="height: 300px"></div>
                    </div>
                </div>
                @error('outside_location')
                    <p class="text-danger">
                        {{ $message }}
                    </p>
                @enderror
                @error('physical_location')
                    <p class="text-danger">
                        {{ $message }}
                    </p>
                @enderror
            </div>
            {{-- Physical & Outside Location End --}}


            <!-- Thumbnail Image -->
            <div class="col-md-4 add-service-thumbnail">
                <label for="thumbnail-secondary" class="form-label form-input-labels">Thumbnail
                    Image<span class="text-danger">*</span></label>
                <div class="position-relative  form-add-category">
                    <div class="image-input {{ $service->image ?? null ? 'image-input-changed' : 'image-input-empty' }}"
                        data-kt-image-input="true">
                        <div class="image-input-wrapper"
                            style="background-image: url('{{ asset('website') . '/' . ($service->image ?? '') }}');">
                        </div>
                        <label
                            class="image-label flex-column gap-3 align-items-center justify-content-center btn-active-color-primary shadow"
                            data-kt-image-input-action="change" data-bs-toggle="tooltip" data-bs-dismiss="click"
                            title="Change avatar">
                            <i class="bi bi-upload upload-icon"></i>
                            <span>Upload Image</span>
                            <span>50x50 px</span>
                            <input type="file" name="thumbnail" accept=".png, .jpg, .jpeg" />
                        </label>
                        <span
                            class="btn btn-icon btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
                            data-kt-image-input-action="cancel" data-bs-toggle="tooltip" data-bs-dismiss="click"
                            title="Cancel avatar">
                            <i class="ki-outline ki-cross fs-3"></i>
                        </span>
                        <span
                            class="btn btn-icon btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
                            data-kt-image-input-action="remove" data-bs-toggle="tooltip" data-bs-dismiss="click"
                            title="Remove avatar">
                            <i class="ki-outline ki-cross fs-3"></i>
                        </span>
                    </div>
                    <label id="thumbnail-error" class="error" for="thumbnail"></label>
                </div>
                @error('thumbnail')
                    <p class="text-danger">
                        {{ $message }}
                    </p>
                @enderror
            </div>
            <div class="">
                <button type="submit" class="add-btn">
                    {{ $btn_text ?? 'Add' }}
                </button>
            </div>
        </div>
    </form>
</div>
@push('js')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.5/jquery.validate.min.js"></script>
    <script>
        $(document).ready(function() {
            setTimeout(function() {
                if (typeof $.fn.validate !== 'undefined') {
                    $.validator.addMethod('maxFileSize', function(value, element, maxSizeKB) {
                        if (element.files.length === 0) {
                            return true; // no file selected, let 'required' rule handle this
                        }
                        const fileSizeKB = element.files[0].size / 1024; // size in KB
                        return fileSizeKB <= maxSizeKB;
                    }, 'File size must be less than {0} KB.');
                    // Add custom validation method for role-based requirements
                    $.validator.addMethod('requiredForRole', function(value, element, params) {
                        const userRole = params.role;
                        const currentUserRole =
                            '{{ auth()->user()->getRoleNames()->first() }}';

                        // If current user has the specified role, field is required
                        if (currentUserRole === userRole) {
                            return value && value.length > 0;
                        }
                        // If user doesn't have the role, field is not required
                        return true;
                    }, 'This field is required for your role.');

                    // Add custom validation method for conditional requirements based on checkboxes
                    $.validator.addMethod('requiredIfChecked', function(value, element, params) {
                        const checkboxSelector = params.checkbox;
                        const isChecked = $(checkboxSelector).is(':checked');

                        // If checkbox is checked, field is required
                        if (isChecked) {
                            return value && value.trim().length > 0;
                        }
                        // If checkbox is not checked, field is not required
                        return true;
                    }, 'This field is required when the related option is selected.');

                    // Add validation for multiple roles
                    $.validator.addMethod('requiredForRoles', function(value, element, params) {
                        const requiredRoles = params.roles; // Array of roles
                        const currentUserRole =
                            '{{ auth()->user()->getRoleNames()->first() }}';

                        // If current user has any of the specified roles, field is required
                        if (requiredRoles.includes(currentUserRole)) {
                            return value && value.length > 0;
                        }
                        return true;
                    }, 'This field is required for your role.');
                    $("#groupServiceForm").validate({
                        debug: false,
                        rules: {
                            thumbnail: {
                                required: true,
                                maxFileSize: 5120
                            },
                            name: {
                                required: true,
                                minlength: 2
                            },
                            category_id: {
                                required: true
                            },
                            subcategory_id: {
                                required: true
                            },
                            'staff_ids[]': {
                                requiredForRole: {
                                    role: 'business'
                                }
                            },
                            physical_location: {
                                requiredIfChecked: {
                                    checkbox: '#onsite-radio'
                                }
                            },
                            outside_location: {
                                requiredIfChecked: {
                                    checkbox: '#outside-location-radio'
                                }
                            },
                            duration: {
                                required: true
                            },
                            price_per_slot: {
                                required: true,
                                number: true,
                                min: 0
                            },
                            additional_cost: {
                                required: true,
                                number: true,
                                min: 0
                            },
                            required_items: {
                                required: true,
                                maxlength: 1000
                            },
                            description: {
                                required: true,
                                maxlength: 1000
                            },
                            discount_recurring: {
                                required: true,
                                number: true,
                                min: 0,
                                max: 100
                            },
                            total_slots: {
                                required: true,
                                number: true,
                                min: 1
                            }
                        },
                        messages: {
                            thumbnail: {
                                required: "Please upload a thumbnail",
                                maxFileSize: "Image size must not exceed 5 MB"
                            },
                            name: {
                                required: "Service name is required",
                                minlength: "Service name must be at least 2 characters"
                            },
                            category_id: {
                                required: "Please select a category"
                            },
                            subcategory_id: {
                                required: "Please select a subcategory"
                            },
                            'staff_ids[]': {
                                requiredForRole: "Please assign at least one staff member to this service"
                            },
                            physical_location: {
                                requiredIfChecked: "Physical location is required when On-site is selected"
                            },
                            outside_location: {
                                requiredIfChecked: "Outside location is required when Outside Location is selected"
                            },
                            duration: {
                                required: "Please select service duration"
                            },
                            price_per_slot: {
                                required: "Service price is required",
                                number: "Please enter a valid price",
                                min: "Price cannot be negative"
                            },
                            additional_cost: {
                                required: "Additional cost is required",
                                number: "Please enter a valid additional cost",
                                min: "Additional cost cannot be negative"
                            },
                            required_items: {
                                required: "Required items is required",
                                maxlength: "Required items cannot exceed 1000 characters"
                            },
                            description: {
                                required: "Description is required",
                                maxlength: "Description cannot exceed 1000 characters"
                            },
                            discount_recurring: {
                                required: "Discount for recurring is required",
                                number: "Please enter a valid discount percentage",
                                min: "Discount percentage must be at least 0%",
                                max: "Discount percentage cannot exceed 100%"
                            },
                            total_slots: {
                                required: "Total slots is required",
                                number: "Please enter a valid number of slots",
                                min: "Total slots must be at least 1"
                            }
                        },
                        submitHandler: function(form) {
                            console.log('Form validation passed, submitting...');
                            // Update availability data
                            saveCurrentWeekData();

                            // Check if getSelectedAvailability function exists
                            if (typeof getSelectedAvailability === 'function') {
                                var selectedData = getSelectedAvailability();
                                // Add availability data to form
                                var availabilityInput = $('<input>').attr({
                                    type: 'hidden',
                                    name: 'availabilities_dates',
                                    value: JSON.stringify(selectedData)
                                });
                                $(form).find('input[name="availabilities_dates"]').remove();
                                $(form).append(availabilityInput);
                            }

                            form.submit();
                        },
                        invalidHandler: function(event, validator) {
                            console.log('Form validation failed. Errors:', validator
                                .numberOfInvalids());
                        }
                    });

                    // Re-validate when radio buttons change to update conditional requirements
                    $('#onsite-radio, #outside-location-radio').on('change', function() {
                        // Clear previous validation errors for conditional fields
                        $('#pac-input, #outside-location-input').removeClass('error');
                        $('.error[for="physical_location"], .error[for="outside_location"]').remove();

                        // Re-validate the form to apply new conditional rules
                        $("#groupServiceForm").valid();
                    });
                } else {
                    console.error('jQuery validate is not available');
                }
            }, 500);

            // Initialize data from JSON
            initializeDataFromJSON();
            updateWeekUI();
            updateJsonOutput();

            // Availability validation removed for now

            $(document).on("change", ".day-checkbox", function() {
                saveCurrentWeekData();
                updateWeekUI();
                updateJsonOutput(); // Update JSON when checkbox changes
            });

            // Validate time inputs when they change
            $(document).on("change", ".start-time, .end-time", function() {
                validateTimeInput(this);
                saveCurrentWeekData();
                updateJsonOutput(); // Update JSON when time changes
            });

            $("#prevWeek").click(function() {
                saveCurrentWeekData();
                currentWeekIndex--;
                updateWeekUI();
                updateJsonOutput(); // Update JSON when week changes
            });

            $("#nextWeek").click(function() {
                saveCurrentWeekData();
                currentWeekIndex++;
                updateWeekUI();
                updateJsonOutput(); // Update JSON when week changes
            });

            $("#saveAvailability").click(function() {
                saveCurrentWeekData();
                updateJsonOutput(); // Final update of JSON
                alert("Availability Saved!");
            });

            // Recurring Radio Button Change
            $("input[name='recurring']").change(function() {
                const selected = $(this).val();
                saveCurrentWeekData();

                if (selected === "custom") {
                    $(".custom-weeks-input").show();
                } else {
                    $(".custom-weeks-input").hide();
                }

                if (selected === "custom") {
                    return false;
                }

                const repeatWeeks = parseInt(selected);

                if (repeatWeeks > 0) {
                    duplicateWeeks(repeatWeeks);
                    updateJsonOutput(); // Update JSON after duplication
                    alert(`Availability reset and duplicated for ${repeatWeeks} weeks total.`);
                }
            });

            // Handle Custom Weeks Input
            $("#customDone").click(function() {
                const customWeeks = parseInt($("#customWeeks").val());
                if (!isNaN(customWeeks) && customWeeks > 0) {
                    saveCurrentWeekData();
                    duplicateWeeks(customWeeks);
                    updateJsonOutput(); // Update JSON after custom duplication
                    alert(`Availability reset and duplicated for ${customWeeks} weeks total.`);
                    $(".custom-weeks-input").hide();
                    $("input[name='recurring']").prop('checked', false); // Uncheck radio buttons
                } else {
                    alert("Please enter a valid number of weeks.");
                }
            });
        });

        // Initialize Google Maps for Group Service
        function initGroupServiceMap() {
            // Only use existing coordinates if we have them in the form fields (from old input or editing)
            const existingLat = document.getElementById('latitude').value;
            const existingLng = document.getElementById('longitude').value;
            const existingLocation = document.getElementById('pac-input').value;

            const userLat = existingLat && existingLat !== '' ? parseFloat(existingLat) : null;
            const userLng = existingLng && existingLng !== '' ? parseFloat(existingLng) : null;
            const userLocation = existingLocation || '';



            let map, marker;

            // Only initialize map if we have existing coordinates
            if (userLat !== null && userLng !== null) {

                // Initialize map with existing coordinates
                map = new google.maps.Map(document.getElementById('map'), {
                    zoom: 15,
                    center: {
                        lat: userLat,
                        lng: userLng
                    }
                });

                // Create marker at existing location
                marker = new google.maps.Marker({
                    position: {
                        lat: userLat,
                        lng: userLng
                    },
                    map: map,
                    draggable: true,
                    title: userLocation || 'Your Business Location'
                });

                // Set the form values
                document.getElementById('latitude').value = userLat;
                document.getElementById('longitude').value = userLng;
            } else {

                // Show placeholder instead of map
                const mapElement = document.getElementById('map');
                mapElement.innerHTML =
                    '<div style="height: 300px; display: flex; align-items: center; justify-content: center; background: #f8f9fa; border: 2px dashed #dee2e6; color: #6c757d; text-align: center;"><div><i class="fas fa-map-marker-alt" style="font-size: 2rem; margin-bottom: 10px;"></i><br>Map will appear after you search for a location</div></div>';
            }

            // Initialize autocomplete
            const input = document.getElementById('pac-input');
            const autocomplete = new google.maps.places.Autocomplete(input);

            // Only bind to map bounds if map exists
            if (map) {
                autocomplete.bindTo('bounds', map);
            }

            // Handle place selection
            autocomplete.addListener('place_changed', function() {
                const place = autocomplete.getPlace();
                if (!place.geometry) {
                    return;
                }

                // Initialize map if it doesn't exist yet
                if (!map) {
                    map = new google.maps.Map(document.getElementById('map'), {
                        zoom: 15,
                        center: place.geometry.location
                    });

                    marker = new google.maps.Marker({
                        position: place.geometry.location,
                        map: map,
                        draggable: true,
                        title: 'Selected Location'
                    });

                    // Add drag listener for new marker
                    marker.addListener('dragend', function() {
                        const position = marker.getPosition();
                        document.getElementById('latitude').value = position.lat();
                        document.getElementById('longitude').value = position.lng();

                        // Reverse geocoding to update address
                        const geocoder = new google.maps.Geocoder();
                        geocoder.geocode({
                            location: position
                        }, function(results, status) {
                            if (status === 'OK' && results[0]) {
                                document.getElementById('pac-input').value = results[0]
                                    .formatted_address;
                            }
                        });
                    });
                } else {
                    // Update existing map and marker
                    if (place.geometry.viewport) {
                        map.fitBounds(place.geometry.viewport);
                    } else {
                        map.setCenter(place.geometry.location);
                        map.setZoom(17);
                    }
                    marker.setPosition(place.geometry.location);
                }

                // Update hidden inputs
                document.getElementById('latitude').value = place.geometry.location.lat();
                document.getElementById('longitude').value = place.geometry.location.lng();
            });

            // Handle marker drag (only if marker exists)
            if (marker) {
                marker.addListener('dragend', function() {
                    const position = marker.getPosition();
                    document.getElementById('latitude').value = position.lat();
                    document.getElementById('longitude').value = position.lng();

                    // Reverse geocoding to update address
                    const geocoder = new google.maps.Geocoder();
                    geocoder.geocode({
                        location: position
                    }, function(results, status) {
                        if (status === 'OK' && results[0]) {
                            document.getElementById('pac-input').value = results[0].formatted_address;
                        }
                    });
                });
            }
        }

        // Initialize Google Maps for Outside Location
        function initOutsideLocationMap() {
            // Only use existing coordinates if we have them in the form fields (from old input or editing)
            const existingLat = document.getElementById('outside-latitude').value;
            const existingLng = document.getElementById('outside-longitude').value;
            const existingLocation = document.getElementById('outside-location-input').value;

            const userLat = existingLat && existingLat !== '' ? parseFloat(existingLat) : null;
            const userLng = existingLng && existingLng !== '' ? parseFloat(existingLng) : null;
            const userLocation = existingLocation || '';

            let map, marker;

            // Only initialize map if we have existing coordinates
            if (userLat !== null && userLng !== null) {
                // Initialize map with existing coordinates
                map = new google.maps.Map(document.getElementById('outside-map'), {
                    zoom: 15,
                    center: {
                        lat: userLat,
                        lng: userLng
                    }
                });

                // Create marker at existing location
                marker = new google.maps.Marker({
                    position: {
                        lat: userLat,
                        lng: userLng
                    },
                    map: map,
                    draggable: true,
                    title: userLocation || 'Outside Location'
                });

                // Set the form values
                document.getElementById('outside-latitude').value = userLat;
                document.getElementById('outside-longitude').value = userLng;
            } else {
                // Show placeholder instead of map
                const mapElement = document.getElementById('outside-map');
                mapElement.innerHTML =
                    '<div style="height: 300px; display: flex; align-items: center; justify-content: center; background: #f8f9fa; border: 2px dashed #dee2e6; color: #6c757d; text-align: center;"><div><i class="fas fa-map-marker-alt" style="font-size: 2rem; margin-bottom: 10px; color: #020C87;"></i><br>Map will appear here after you enter your location</div></div>';
            }

            // Initialize autocomplete
            const input = document.getElementById('outside-location-input');
            const autocomplete = new google.maps.places.Autocomplete(input);

            // Only bind to map bounds if map exists
            if (map) {
                autocomplete.bindTo('bounds', map);
            }

            // Handle place selection
            autocomplete.addListener('place_changed', function() {
                const place = autocomplete.getPlace();
                if (!place.geometry) {
                    return;
                }

                // Initialize map if it doesn't exist yet
                if (!map) {
                    map = new google.maps.Map(document.getElementById('outside-map'), {
                        zoom: 15,
                        center: place.geometry.location
                    });

                    marker = new google.maps.Marker({
                        position: place.geometry.location,
                        map: map,
                        draggable: true,
                        title: 'Selected Location'
                    });

                    // Add drag listener for new marker
                    marker.addListener('dragend', function() {
                        const position = marker.getPosition();
                        document.getElementById('outside-latitude').value = position.lat();
                        document.getElementById('outside-longitude').value = position.lng();

                        // Reverse geocoding to update address
                        const geocoder = new google.maps.Geocoder();
                        geocoder.geocode({
                            location: position
                        }, function(results, status) {
                            if (status === 'OK' && results[0]) {
                                document.getElementById('outside-location-input').value = results[0]
                                    .formatted_address;
                            }
                        });
                    });
                } else {
                    // Update existing map and marker
                    if (place.geometry.viewport) {
                        map.fitBounds(place.geometry.viewport);
                    } else {
                        map.setCenter(place.geometry.location);
                        map.setZoom(17);
                    }
                    marker.setPosition(place.geometry.location);
                }

                // Update hidden inputs
                document.getElementById('outside-latitude').value = place.geometry.location.lat();
                document.getElementById('outside-longitude').value = place.geometry.location.lng();
            });

            // Handle marker drag (only if marker exists)
            if (marker) {
                marker.addListener('dragend', function() {
                    const position = marker.getPosition();
                    document.getElementById('outside-latitude').value = position.lat();
                    document.getElementById('outside-longitude').value = position.lng();

                    // Reverse geocoding to update address
                    const geocoder = new google.maps.Geocoder();
                    geocoder.geocode({
                        location: position
                    }, function(results, status) {
                        if (status === 'OK' && results[0]) {
                            document.getElementById('outside-location-input').value = results[0].formatted_address;
                        }
                    });
                });
            }
        }

        // Initialize when Google Maps is ready
        $(document).ready(function() {
            // Wait for Google Maps to load, then initialize
            function waitForGoogleMaps() {
                if (typeof google !== 'undefined' && typeof google.maps !== 'undefined') {
                    initGroupServiceMap();
                    initOutsideLocationMap();
                } else {
                    setTimeout(waitForGoogleMaps, 100);
                }
            }
            waitForGoogleMaps();
        });
    </script>
@endpush
