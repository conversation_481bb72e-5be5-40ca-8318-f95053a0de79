<div class="col-md-12">
    @if(!isset($service))
        <ul class="nav nav-pills mb-10" id="pills-tab" role="tablist">
            <li class="nav-item" role="presentation">
                <a href="{{ route('services.create', ["type" => "individual"]) }}"
                    class="nav-link {{ $type == 'individual' ? 'active' : '' }}  business-services">@include('svg.individula')
                    Individual Services
                </a>
            </li>
            <li class="nav-item" role="presentation">
                <a class="nav-link business-services {{ $type == 'group' ? 'active' : '' }}"
                    href="{{ route('services.create', ["type" => "group"]) }}">
                    @include('svg.group')
                    Group Service
                </a>
            </li>
        </ul>
    @endif
    <div>
        @includeWhen($type == 'individual', 'dashboard.service.include.individual', ["btn_text" => $btn_text ?? "Add"])
        @includeWhen($type == 'group', 'dashboard.service.include.group', ["btn_text" => $btn_text ?? "Add"])
    </div>

</div>

@push("js")
    <script>

        $('#category').on('select2:select', function (e) {
            $(this).select2('close');
        });

        $(document).ready(function () {
            // Initialize Select2 on page load
            $('#subcategory').select2();

            // Check if there's an old category value (for validation errors)
            var oldCategoryId = '{{ old("category_id") }}';
            var oldSubcategoryId = '{{ old("subcategory_id") }}';

            if (oldCategoryId && oldCategoryId !== '') {
                // Load subcategories for the old category value
                loadSubcategories(oldCategoryId, oldSubcategoryId);
            }

            $(document).on('change', '#category', function () {
                let category_id = $(this).val();
                loadSubcategories(category_id);
            });

            function loadSubcategories(category_id, selectedSubcategoryId = '') {
                let subcategory = $('#subcategory');

                if (category_id != '') {
                    $.ajax({
                        url: `{{ route('services.get-subcategories', ':category_id') }}`.replace(':category_id', category_id),
                        type: 'GET',
                        success: function (response) {
                            if (response.success && response.subcategories) {
                                // Clear existing options
                                subcategory.empty();
                                subcategory.append('<option value="">Select Subcategory</option>');

                                // Add new options
                                response.subcategories.forEach(element => {
                                    var isSelected = selectedSubcategoryId && selectedSubcategoryId == element.ids ? 'selected' : '';
                                    subcategory.append(`<option value="${element.ids}" ${isSelected}>${element.name}</option>`);
                                });

                                // Trigger change to update Select2
                                subcategory.trigger('change');
                            }
                        },
                        error: function (xhr, status, error) {
                            console.error('Error fetching subcategories:', error);
                            alert('An error occurred while fetching subcategories');
                        }
                    });
                } else {
                    // Clear subcategory when no category is selected
                    subcategory.empty();
                    subcategory.append('<option value="">Select Subcategory</option>');
                    subcategory.trigger('change');
                }
            }
        });
    </script>

    <!-- Google Maps API -->
    <script
        src="https://maps.googleapis.com/maps/api/js?key=AIzaSyA1Qkj7ocEmNs4U5-T2vVJ04OrsCRH0tCU&libraries=places&v=weekly"
        async defer></script>
@endpush