<div>
    <form
        action="{{ isset($service) ? route('services.update', ['service' => $service->ids, 'type' => 'individual']) : route('services.store', ['type' => 'individual']) }}"
        method="POST" enctype="multipart/form-data" class="form-add-services" id="individualServiceForm">
        @csrf
        @isset($service)
            @method('PUT')
        @endisset
        <div class="row row-gap-5">
            {{-- Service Name --}}
            <div class="col-md-12">
                <label for="service-name" class="form-label form-input-labels">
                    Service Name<span class="text-danger">*</span>
                </label>
                <input type="text" class="form-control form-inputs-field" placeholder="Enter Service name"
                    id="service-name" name="name" value="{{ old('name', $service->name ?? '') }}">
                @error('name')
                    <p class="text-danger">
                        {{ $message }}
                    </p>
                @enderror
            </div>
            {{-- Service Name End --}}


            {{-- Category Name --}}
            <div class="col-md-6">
                <label for="category" class="form-label form-input-labels">Category<span
                        class="text-danger">*</span></label>
                <select class="form-select form-select-field" data-control="select2" data-dropdown-css-class="w-619px"
                    data-close-on-select="false" data-placeholder="Select an option" id="category" name="category_id">
                    <option></option>
                    @forelse ($categories as $category)
                        <option value="{{ $category->ids }}"
                            {{ old('category_id', $service->category->ids ?? '') == $category->ids ? 'selected' : '' }}>
                            {{ $category->name }}</option>
                    @empty
                        <option value="">No categories found</option>
                    @endforelse
                </select>
                @error('category_id')
                    <p class="text-danger">
                        {{ $message }}
                    </p>
                @enderror

                <label id="category-error" class="error" for="category"></label>
            </div>
            {{-- Category Name End --}}


            {{-- Subcategory --}}
            <div class="col-md-6">
                <label for="subcategory" class="form-label form-input-labels">Sub
                    Category<span class="text-danger">*</span></label>
                <select class="form-select form-select-field" data-control="select2" data-dropdown-css-class="w-619px"
                    data-close-on-select="false" data-placeholder="Select an option" data-allow-clear="true"
                    id="subcategory" name="subcategory_id">
                    <option value="">Select Subcategory</option>
                    @if (isset($service) && $service->category)
                        @forelse ($service->category->subcategories as $subcategory)
                            <option value="{{ $subcategory->ids }}"
                                {{ old('subcategory_id', $service->subcategory->ids ?? '') == $subcategory->ids ? 'selected' : '' }}>
                                {{ $subcategory->name }}</option>
                        @empty
                        @endforelse
                    @endif
                </select>
                @error('subcategory_id')
                    <p class="text-danger">
                        {{ $message }}
                    </p>
                @enderror
                <label id="subcategory-error" class="error" for="subcategory"></label>
            </div>
            {{-- Subcategory End --}}


            @if (auth()->check() && auth()->user()->hasRole('business'))
                <div class="col-md-12">
                    <label for="staff-member" class="form-label form-input-labels">Assign Staff
                        Member<span class="text-danger">*</span></label>
                    <select class="form-select form-select-field" data-control="select2"
                        data-dropdown-css-class="w-619px" data-close-on-select="false"
                        data-placeholder="Select staff members" data-allow-clear="true" multiple="multiple"
                        id="staff-member" name="staff_ids[]">
                        @forelse ($staffs as $staff)
                            <option value="{{ $staff->id }}"
                                {{ isset($service) && $service->staff->contains('id', $staff->id) ? 'selected' : '' }}>
                                {{ $staff->name }}</option>
                        @empty
                            <option disabled>No staff members available</option>
                        @endforelse
                    </select>
                    @error('staff_ids')
                        <p class="text-danger">
                            {{ $message }}
                        </p>
                    @enderror

                    <label id="staff-member-error" class="error" for="staff-member"></label>
                </div>
            @endif

            {{-- Availability --}}
            <div class="col-md-12">
                <label class="form-label form-input-labels">Availability<span class="text-danger">*</span></label>
                <x-service-availability-component :availabilities="$service->availabilities ?? []" />
            </div>
            {{-- Availability End --}}


            {{-- Service Duration --}}
            <div class="col-md-6">
                <label for="duration" class="form-label form-input-labels ">Service Duration<span
                        class="text-danger">*</span></label>
                <select name="duration" id="duration" class="form-control text-muted  form-select-field">
                    <option value="">Select Service Duration</option>
                    <option value="15" {{ old('duration', $service->duration ?? '') == '15' ? 'selected' : '' }}>15
                        min
                    </option>
                    <option value="30" {{ old('duration', $service->duration ?? '') == '30' ? 'selected' : '' }}>30
                        min
                    </option>
                    <option value="45" {{ old('duration', $service->duration ?? '') == '45' ? 'selected' : '' }}>40
                        min
                    </option>
                    <option value="60" {{ old('duration', $service->duration ?? '') == '60' ? 'selected' : '' }}>60
                        min
                    </option>
                </select>
                {{-- <input type="number" class="form-control form-inputs-field"
                    placeholder="Enter Services Duration" id="duration" name="duration"> --}}
                <!-- <select class="form-select form-select-field" id="duration" name="duration"
                    data-control="select2" data-placeholder="Select">
                    <option></option>
                    <option value="30 min">30 min</option>
                    <option value="40 min">40 min</option>
                </select> -->
                @error('duration')
                    <p class="text-danger">
                        {{ $message }}
                    </p>
                @enderror
            </div>
            {{-- Service Duration End --}}


            {{-- Price --}}
            <div class="col-md-3">
                <label for="price" class="form-label form-input-labels">
                    Price <span class="normal opacity-6 light-black">(Inclusive VAT)<span
                            class="text-danger">*</span></span>
                </label>
                <input type="number" class="form-control form-inputs-field" placeholder="Enter price" id="price"
                    name="price" value="{{ old('price', $service->price ?? '') }}">
                @error('price')
                    <p class="text-danger">
                        {{ $message }}
                    </p>
                @enderror
            </div>
            {{-- Price End --}}

            {{-- Additional Costs --}}
            <div class="col-md-3">
                <label for="tax" class="form-label form-input-labels">Additional Costs<span
                        class="text-danger">*</span></label>
                <input type="number" class="form-control form-inputs-field" placeholder="Enter additional cost"
                    id="tax" name="additional_cost"
                    value="{{ old('additional_cost', $service->additional_cost ?? '') }}">
                @error('additional_cost')
                    <p class="text-danger">
                        {{ $message }}
                    </p>
                @enderror
            </div>
            {{-- Additional Costs End --}}

            {{-- Required Items --}}
            <div class="col-md-12">
                <label for="required-items" class="form-label form-input-labels">Required
                    Items For Service<span class="text-danger">*</span></label>
                <input type="text" class="form-control form-inputs-field"
                    placeholder="Enter required items (separate with comma)" id="required-items"
                    name="required_items" value="{{ old('required_items', $service->required_items ?? '') }}">
                @error('required_items')
                    <p class="text-danger">
                        {{ $message }}
                    </p>
                @enderror
            </div>
            {{-- Required Items End --}}

            {{-- Description --}}
            <div class="col-md-12">
                <label for="description" class="form-label form-input-labels">Description<span
                        class="text-danger">*</span></label>
                <textarea class="form-control form-inputs-field form-textarea-field" id="description" name="description"
                    rows="4" placeholder="Enter Description here">{{ old('description', $service->description ?? '') }}</textarea>
                @error('description')
                    <p class="text-danger">
                        {{ $message }}
                    </p>
                @enderror
            </div>
            {{-- Description End --}}

            {{-- Onsite & Customer Location --}}
            <div class="col-md-12">
                <label class="form-label form-input-labels">Service Location<span class="text-danger">*</span></label>
                <div class="d-flex gap-4">
                    <label class="styled-checkbox d-flex gap-3">
                        <input type="checkbox" name="is_onsite" value="1" id="onsite-secondary"
                            @checked(old('is_onsite', $service->is_onsite ?? false))>
                        <span class="fs-14 light-black normal">On-site</span>
                    </label>

                    <label class="styled-checkbox d-flex gap-3">
                        <input type="checkbox" name="is_customer_location" value="1"
                            @checked(old('is_customer_location', $service->is_customer_location ?? false)) id="customer-location-secondary">
                        <span class="fs-14 light-black normal">Customer Location</span>
                    </label>
                </div>
                @error('is_onsite')
                    <p class="text-danger">
                        {{ $message }}
                    </p>
                @enderror
            </div>
            {{-- Onsite & Customer Location End --}}

            <div class="row row-gap-5">
                {{-- Physical Location --}}
                <div class="col-md-12 form-hide-box" id="physical-location-field">
                    <label for="pac-input" class="form-label form-input-labels">
                        Physical Location
                    </label>
                    <input type="text" class="form-control form-inputs-field form-textarea-field" id="pac-input"
                        name="physical_location" value="{{ old('physical_location') }}"
                        placeholder="Your registered business location">

                    <input type="hidden" name="lat" value="{{ old('lat') }}" id="latitude">

                    <input type="hidden" name="lng" value="{{ old('lng') }}" id="longitude">

                    <div class="custom_loc mt-2">
                        <div id="map" style="height: 300px;"></div>
                    </div>

                    @error('physical_location')
                        <p class="text-danger">{{ $message }}</p>
                    @enderror
                </div>

                {{-- Physical Location End --}}

                {{-- Radius, Travel Time, Additional Service Charges --}}
                <div class="col-md-4 form-hide-box" id="radius-field">
                    <label for="radius" class="form-label form-input-labels">Radius</label>
                    <input type="number" class="form-control form-inputs-field" placeholder="Enter radius"
                        value="{{ old('radius', $service->radius ?? '') }}" id="radius" name="radius">
                    @error('radius')
                        <p class="text-danger">
                            {{ $message }}
                        </p>
                    @enderror
                </div>

                <div class="col-md-4 form-hide-box" id="traveltime-field">
                    <label for="traveltime" class="form-label form-input-labels">Travel
                        Time</label>
                    <input type="number" class="form-control form-inputs-field"
                        value="{{ old('travel_time', $service->travel_time ?? '') }}" placeholder="Enter travel time"
                        id="traveltime" name="travel_time">
                    @error('travel_time')
                        <p class="text-danger">
                            {{ $message }}
                        </p>
                    @enderror
                </div>

                <div class="col-md-4 form-hide-box" id="servicecharges-field">
                    <label for="servicecharges" class="form-label form-input-labels">Additional
                        Service Charges</label>
                    <input type="number" class="form-control form-inputs-field"
                        placeholder="Enter additional service charges" id="servicecharges" name="service_charges"
                        value="{{ old('service_charges', $service->service_charges ?? '') }}">
                    @error('service_charges')
                        <p class="text-danger">
                            {{ $message }}
                        </p>
                    @enderror
                </div>
            </div>

            {{-- Thumbnail Image --}}
            <div class="col-md-4 add-service-thumbnail">
                <label for="thumbnail-secondary" class="form-label form-input-labels ">Thumbnail
                    Image<span class="text-danger">*</span></label>
                <div class="position-relative  form-add-category">
                    <div class="image-input {{ $service->image ?? null ? 'image-input-changed' : 'image-input-empty' }}"
                        data-kt-image-input="true">
                        <div class="image-input-wrapper"
                            style="background-image: url('{{ asset('website') . '/' . ($service->image ?? '') }}');">
                        </div>
                        <label
                            class="image-label flex-column gap-3 align-items-center justify-content-center btn-active-color-primary shadow"
                            data-kt-image-input-action="change" data-bs-toggle="tooltip" data-bs-dismiss="click"
                            title="Change avatar">
                            <i class="bi bi-upload upload-icon"></i>
                            <span>Upload Image</span>
                            <span>50x50 px</span>
                            <input type="file" name="thumbnail" accept=".png, .jpg, .jpeg" />
                        </label>

                        <span
                            class="btn btn-icon btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
                            data-kt-image-input-action="cancel" data-bs-toggle="tooltip" data-bs-dismiss="click"
                            title="Cancel avatar">
                            <i class="ki-outline ki-cross fs-3"></i>
                        </span>
                        <span
                            class="btn btn-icon btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
                            data-kt-image-input-action="remove" data-bs-toggle="tooltip" data-bs-dismiss="click"
                            title="Remove avatar">
                            <i class="ki-outline ki-cross fs-3"></i>
                        </span>
                    </div>
                    <label id="thumbnail-error" class="error" for="thumbnail"></label>
                </div>
                @error('thumbnail')
                    <p class="text-danger">
                        {{ $message }}
                    </p>
                @enderror
            </div>
            {{-- Thumbnail Image End --}}
            <div class="">
                <button type="submit" class="add-btn">
                    {{ $btn_text ?? 'Add' }}
                </button>
            </div>
        </div>
    </form>
</div>
@push('js')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.5/jquery.validate.min.js"></script>
    <script>
        // Individual service form validation only - availability handled by component
        $(document).ready(function() {
            setTimeout(function() {
                if (typeof $.fn.validate !== 'undefined') {
                    $.validator.addMethod('maxFileSize', function(value, element, maxSizeKB) {
                        if (element.files.length === 0) {
                            return true;
                        }
                        const fileSizeKB = element.files[0].size / 1024;
                        return fileSizeKB <= maxSizeKB;
                    }, 'File size must be less than {0} KB.');

                    $.validator.addMethod('requiredForRole', function(value, element, params) {
                        const userRole = params.role;
                        const currentUserRole = '{{ auth()->user()->getRoleNames()->first() }}';
                        if (currentUserRole === userRole) {
                            return value && value.length > 0;
                        }
                        return true;
                    }, 'This field is required for your role.');

                    $.validator.addMethod('requiredIfChecked', function(value, element, params) {
                        const checkboxSelector = params.checkbox;
                        const isChecked = $(checkboxSelector).is(':checked');
                        if (isChecked) {
                            return value && value.trim().length > 0;
                        }
                        return true;
                    }, 'This field is required when the related option is selected.');

                    $('#onsite-secondary, #customer-location-secondary').on('change', function() {
                        const onsiteChecked = $('#onsite-secondary').is(':checked');
                        const customerLocationChecked = $('#customer-location-secondary').is(':checked');

                        if (!onsiteChecked && !customerLocationChecked) {
                            if ($('.service-location-error').length === 0) {
                                const errorMsg = '<div class="service-location-error text-danger mt-2">Please select at least one service location option.</div>';
                                $('#customer-location-secondary').closest('.col-md-6').append(errorMsg);
                            }
                            return false;
                        } else {
                            $('.service-location-error').remove();
                        }
                    });

                    $("#individualServiceForm").validate({
                        submitHandler: function(form) {
                            // Get availability data from the component
                            const availabilityData = window.getAvailabilityData ? window.getAvailabilityData() : [];

                            // Add availability data to form
                            var availabilityInput = $('<input>').attr({
                                type: 'hidden',
                                name: 'availabilities_dates',
                                value: JSON.stringify(availabilityData)
                            });
                            $(form).find('input[name="availabilities_dates"]').remove();
                            $(form).append(availabilityInput);
                            form.submit();
                        },
                        rules: {
                            thumbnail: {
                                required: true,
                                maxFileSize: 5120
                            },
                            name: {
                                required: true,
                                minlength: 2
                            },
                            category_id: {
                                required: true
                            },
                            subcategory_id: {
                                required: true
                            },
                            physical_location: {
                                requiredIfChecked: {
                                    checkbox: '#onsite-secondary'
                                }
                            },
                            radius: {
                                requiredIfChecked: {
                                    checkbox: '#customer-location-secondary'
                                },
                                number: true,
                                min: 1
                            },
                            travel_time: {
                                requiredIfChecked: {
                                    checkbox: '#customer-location-secondary'
                                },
                                number: true,
                                min: 1
                            },
                            service_charges: {
                                requiredIfChecked: {
                                    checkbox: '#customer-location-secondary'
                                },
                                number: true,
                                min: 0
                            },
                            duration: {
                                required: true
                            },
                            price: {
                                required: true,
                                number: true,
                                min: 0
                            },
                            additional_cost: {
                                required: true,
                                number: true,
                                min: 0
                            },
                            required_items: {
                                required: true,
                                maxlength: 1000
                            },
                            description: {
                                required: true,
                                maxlength: 1000
                            }
                        },
                        messages: {
                            thumbnail: {
                                required: "Please upload a thumbnail",
                                maxFileSize: "Image size must not exceed 5 MB"
                            },
                            name: {
                                required: "Service name is required",
                                minlength: "Service name must be at least 2 characters"
                            },
                            category_id: {
                                required: "Please select a category"
                            },
                            subcategory_id: {
                                required: "Please select a subcategory"
                            },
                            physical_location: {
                                requiredIfChecked: "Physical location is required when On-site is selected"
                            },
                            radius: {
                                requiredIfChecked: "Radius is required when Customer Location is selected",
                                number: "Please enter a valid number",
                                min: "Radius must be at least 1 km"
                            },
                            travel_time: {
                                requiredIfChecked: "Travel time is required when Customer Location is selected",
                                number: "Please enter a valid number",
                                min: "Travel time must be at least 1 minute"
                            },
                            service_charges: {
                                requiredIfChecked: "Service charges are required when Customer Location is selected",
                                number: "Please enter a valid number",
                                min: "Service charges cannot be negative"
                            },
                            duration: {
                                required: "Duration is required"
                            },
                            price: {
                                required: "Price is required",
                                number: "Please enter a valid number",
                                min: "Price cannot be negative"
                            },
                            additional_cost: {
                                required: "Additional cost is required",
                                number: "Please enter a valid number",
                                min: "Additional cost cannot be negative"
                            },
                            required_items: {
                                required: "Required items field is required",
                                maxlength: "Required items cannot exceed 1000 characters"
                            },
                            description: {
                                required: "Description is required",
                                maxlength: "Description cannot exceed 1000 characters"
                            }
                        }
                    });
                }
            }, 100);
        });

        // Initialize Google Maps for Individual Service
        function initIndividualServiceMap() {
            const userLat = {{ auth()->user()->profile->lat ?? 'null' }};
            const userLng = {{ auth()->user()->profile->lng ?? 'null' }};
            const userLocation = "{{ auth()->user()->profile->location ?? '' }}";

            let map, marker;

            if (userLat !== null && userLng !== null) {
                map = new google.maps.Map(document.getElementById('map'), {
                    zoom: 15,
                    center: { lat: userLat, lng: userLng }
                });

                marker = new google.maps.Marker({
                    position: { lat: userLat, lng: userLng },
                    map: map,
                    draggable: true,
                    title: userLocation || 'Service Location'
                });

                document.getElementById('latitude').value = userLat;
                document.getElementById('longitude').value = userLng;
            } else {
                const mapElement = document.getElementById('map');
                mapElement.innerHTML = '<div style="height: 300px; display: flex; align-items: center; justify-content: center; background: #f8f9fa; border: 2px dashed #dee2e6; color: #6c757d; text-align: center;"><div><i class="fas fa-map-marker-alt" style="font-size: 2rem; margin-bottom: 10px; color: #020C87;"></i><br>Map will appear here after you enter your location</div></div>';
            }

            const input = document.getElementById('pac-input');
            const autocomplete = new google.maps.places.Autocomplete(input);

            if (map) {
                autocomplete.bindTo('bounds', map);
            }

            autocomplete.addListener('place_changed', function() {
                const place = autocomplete.getPlace();
                if (!place.geometry) return;

                if (!map) {
                    map = new google.maps.Map(document.getElementById('map'), {
                        zoom: 15,
                        center: place.geometry.location
                    });

                    marker = new google.maps.Marker({
                        position: place.geometry.location,
                        map: map,
                        draggable: true,
                        title: 'Selected Location'
                    });

                    marker.addListener('dragend', function() {
                        const position = marker.getPosition();
                        document.getElementById('latitude').value = position.lat();
                        document.getElementById('longitude').value = position.lng();

                        const geocoder = new google.maps.Geocoder();
                        geocoder.geocode({ location: position }, function(results, status) {
                            if (status === 'OK' && results[0]) {
                                document.getElementById('pac-input').value = results[0].formatted_address;
                            }
                        });
                    });
                } else {
                    if (place.geometry.viewport) {
                        map.fitBounds(place.geometry.viewport);
                    } else {
                        map.setCenter(place.geometry.location);
                        map.setZoom(17);
                    }
                    marker.setPosition(place.geometry.location);
                }

                document.getElementById('latitude').value = place.geometry.location.lat();
                document.getElementById('longitude').value = place.geometry.location.lng();
            });

            if (marker) {
                marker.addListener('dragend', function() {
                    const position = marker.getPosition();
                    document.getElementById('latitude').value = position.lat();
                    document.getElementById('longitude').value = position.lng();

                    const geocoder = new google.maps.Geocoder();
                    geocoder.geocode({ location: position }, function(results, status) {
                        if (status === 'OK' && results[0]) {
                            document.getElementById('pac-input').value = results[0].formatted_address;
                        }
                    });
                });
            }
        }

        // Initialize when Google Maps is ready
        $(document).ready(function() {
            function waitForGoogleMaps() {
                if (typeof google !== 'undefined' && typeof google.maps !== 'undefined') {
                    initIndividualServiceMap();
                } else {
                    setTimeout(waitForGoogleMaps, 100);
                }
            }
            waitForGoogleMaps();
        });
    </script>
@endpush
        
