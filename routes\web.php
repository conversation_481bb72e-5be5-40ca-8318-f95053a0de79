<?php

use App\Http\Controllers\Auth\LoginController;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\BookingController;
use App\Http\Controllers\CategoriesController;
use App\Http\Controllers\CustomerController;
use App\Http\Controllers\CmsController;
use App\Http\Controllers\CronController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\ThemeController;
use App\Http\Controllers\RoleController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\CrudGeneratorController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\DiscountCouponsController;
use App\Http\Controllers\EmailVerificationController;
use App\Http\Controllers\FriendsController;
use App\Http\Controllers\GoogleCalendarController;
use App\Http\Controllers\ProfessionalController;
use App\Http\Controllers\ServiceController;
use App\Http\Controllers\SubCategoriesController;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\WebsiteController;
use App\Http\Controllers\SocialAuthController;
use App\Http\Controllers\StaffController;
use App\Http\Controllers\StripeController;
use App\Http\Controllers\StripeConnectController;
use App\Http\Controllers\TrustpilotController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/


Route::get("/clear-all", [WebsiteController::class, "clear_all"]);

Route::get('/', [WebsiteController::class, 'index'])->name('home')->middleware('user_check');

Route::get('/logout', function () {
    // Update user online status before logout
    if (Auth::check()) {
        $user = Auth::user();
        $user->update([
            'is_online' => false,
            'online_at' => now()
        ]);

        // Broadcast user offline status
        broadcast(new \App\Events\UserOffline($user->id, now()));
    }

    session()->flush();
    Auth::logout();
    return redirect('/'); // Redirect the user after logout
});



// Zohaib Route
Route::get('send-otp', [EmailVerificationController::class, 'sendOtp'])->name('send_otp');
Route::post('verify-otp', [EmailVerificationController::class, 'verifyOtp'])->name('verify_otp');
Route::post('resend-otp', [EmailVerificationController::class, 'resendOtp'])->name('resend_otp');
Route::post("set-password", [AuthController::class, "set_password"])->name('set_password');

Route::middleware(['auth', "verified"])->group(function () {
    Route::get("register/{user_type}", [AuthController::class, 'registerUserType'])->name('register.user_type')->where("user_type", "customer|professional");
    Route::post("register/customer", [AuthController::class, 'registerCustomer'])->name('register.customer');

    // Professional registration step-wise routes
    Route::post("register/professional/save-step", [AuthController::class, 'saveStepData'])->name('register.professional.save_step');
    Route::get("register/professional/get-progress", [AuthController::class, 'getProgress'])->name('register.professional.get_progress');
});

Route::get('crud_generator', [CrudGeneratorController::class, 'crudGenerator'])->name('crud_generator');
Route::post('crud_generator_process', [CrudGeneratorController::class, 'crudGeneratorProcess'])->name('crud_generator_process');

Route::get('permissions', [ThemeController::class, 'permissions'])->name('permissions')->middleware('auth');

// Custom login routes with admin prefix
Route::prefix('admin')->group(function () {
    // Login Routes only
    Route::get('login', [LoginController::class, 'showLoginForm'])->name('login');
    Route::post('login', [LoginController::class, 'login']);
});

// All other authentication routes (register, password reset, etc.) - keep default paths
Auth::routes(['login' => false]); // Exclude login since we defined it above

// Block access to plain /login route
Route::get('/login', function () {
    abort(404);
});

Route::get('search_treatments', [WebsiteController::class, 'searchTreatments'])->name('search_treatments');
Route::get('/home', [ThemeController::class, 'dashboard'])->name('dashboard')->middleware(['auth', 'role:developer']);

// Route::fallback(function(){
// route(404);
// });
// Google OAuth Routes
//Route::get("register/{user_type}", [AuthController::class, 'registerUserType'])->name('register.user_type')->where("user_type", "customer|professional");
// Social login routes with user type parameter
Route::get('/auth/google/{user_type}', [SocialAuthController::class, 'redirectToGoogle'])->name('google.login.with.type')->where("user_type", "customer|professional");
Route::get('/auth/google/callback', [SocialAuthController::class, 'handleGoogleCallback'])->name('google.callback');

Route::get('/auth/apple/{user_type}', [SocialAuthController::class, 'redirectToApple'])->name('apple.login.with.type')->where("user_type", "customer|professional");
Route::get('/auth/apple/callback', [SocialAuthController::class, 'handleAppleCallback'])->name('apple.callback');

// Google Calendar Routes
Route::get('/google-calendar/connect', [GoogleCalendarController::class, 'redirectToGoogle'])->name('google.calendar.connect');
Route::get('/google-calendar/callback', [GoogleCalendarController::class, 'handleGoogleCallback']);

Route::get('/email/verify', function () {
    return view('auth.verify-email');
})->middleware('auth')->name('verification.notice');

// Route::middleware(['auth', "verified"])->group(function () {
//     Route::get("dashboard", function () {
//         return "the email is verified";
//     });
//     Route::get('/dashboard', [ThemeController::class, 'dashboard'])->name('dashboard');
// });

Route::group(['middleware' => ['auth']], function () {
    Route::resource('roles', RoleController::class);
    //Route::resource('roles/{id?}', RoleController::class)->name('roles.edit');
    Route::resource('users', UserController::class);
});

// website  route
Route::get('/services-all/{category?}/{subcategory?}', [WebsiteController::class, 'services'])->name('website_services');
Route::get("filter-services", [WebsiteController::class, 'filterServices'])->name('filter_services');
Route::get("get-service-details/{id?}/{date?}", [WebsiteController::class, 'getServiceDetails'])->name('get_service_details');
Route::get("get-service-time-slots/{id}", [WebsiteController::class, 'getServiceTimeSlots'])->name('get_service_time_slots');
Route::get("get-service-availabilies-details/{id?}", [WebsiteController::class, 'getServiceAvailabiliesDetails'])->name('get_service_availabilies_details');
Route::get("get-family-details/{type?}", [WebsiteController::class, 'getfamilyDetails'])->name('get_family_details');
Route::get("filter-professional", [WebsiteController::class, 'filterProfessional'])->name('filter_professional');
Route::get('professional/{category?}/{subcategory?}', [WebsiteController::class, 'professional'])->name('professional');
Route::get('privacy_policy', [WebsiteController::class, 'privacyPolicy'])->name('privacy_policy');
Route::get('terms', [WebsiteController::class, 'terms'])->name('terms');
Route::get('page/{slug}', [WebsiteController::class, 'showPage'])->name('page.show');
Route::get('professional-profile/{id}', [WebsiteController::class, 'professional_profile'])->name('professional_profile');
Route::post('generate-short-url', [WebsiteController::class, 'generateShortUrl'])->name('generate_short_url');
Route::get('p/{shortUrl}', [WebsiteController::class, 'redirectShortUrl'])->name('short_url_redirect');

//Common routes for all authenticated users
Route::group(['middleware' => ['auth']], function () {
    Route::post('personal-info/update', [CustomerController::class, 'updatePersonalInfo'])->name('personal-info.update');
    Route::post('socials/store', [CustomerController::class, 'storeSocials'])->name('socials.store');
    Route::post('update-password', [DashboardController::class, 'updatePassword'])->name('update.password');
    Route::get('notification', [ThemeController::class, 'notification'])->name('notification');
    Route::get('notifications/unread-count', [ThemeController::class, 'getUnreadNotificationCount'])->name('notifications.unread_count');
    Route::post('notifications/mark-all-read', [ThemeController::class, 'markAllAsRead'])->name('notifications.mark_all_read');
    Route::post('notifications/{id}/mark-read', [ThemeController::class, 'markAsRead'])->name('notifications.mark_read');
        Route::post('check-current-password', [DashboardController::class, 'checkCurrentPassword'])->name('check.current.password');

    // Chat routes for all authenticated users
    Route::prefix('chats')->group(function () {
        Route::get('/', [\App\Http\Controllers\ChatController::class, 'index'])->name('chats.index');
        Route::get('/conversations', [\App\Http\Controllers\ChatController::class, 'getConversations'])->name('chats.conversations');
        Route::get('/messages/{conversation}', [\App\Http\Controllers\ChatController::class, 'getMessages'])->name('chats.messages');
        Route::post('/send', [\App\Http\Controllers\ChatController::class, 'sendMessage'])->name('chats.send');
        Route::post('/upload-files', [\App\Http\Controllers\ChatController::class, 'uploadFiles'])->name('chats.upload_files');
        Route::post('/messages/{message}/delivered', [\App\Http\Controllers\ChatController::class, 'markAsDelivered'])->name('chats.mark_delivered');
        Route::post('/mark-read/{conversation}', [\App\Http\Controllers\ChatController::class, 'markAsRead'])->name('chats.mark_read');
        Route::post('/typing/{conversation}', [\App\Http\Controllers\ChatController::class, 'typing'])->name('chats.typing');
        Route::post('/conversations/{conversation}/archive', [\App\Http\Controllers\ChatController::class, 'archiveConversation'])->name('chats.archive');
        Route::delete('/conversations/{conversation}', [\App\Http\Controllers\ChatController::class, 'deleteConversation'])->name('chats.delete');
        Route::get('/unread-count', [\App\Http\Controllers\ChatController::class, 'getUnreadCount'])->name('chats.unread_count');
    });
});

//customer routes
Route::group(['middleware' => ['auth', 'role:customer', 'verified', 'user_check']], function () {
    Route::prefix('customer')->group(function () {
        //Customer Friends Routes
        Route::resource('friends', "\App\Http\Controllers\FriendsController");
        Route::prefix('friends')->group(function () {
            Route::patch('{id}/accept', [FriendsController::class, 'accept'])->name('friends.accept');
            Route::patch('{id}/reject', [FriendsController::class, 'reject'])->name('friends.reject');
            Route::get('family_friends_details', [ThemeController::class, 'friendsDetails'])->name('family_friends_details');
        });
        Route::get('setting', [DashboardController::class, 'setting'])->name('customer.setting');
        Route::get('my-booking', [BookingController::class, 'customerBooking'])->name('customer_booking');
        Route::get('my-booking/calendar-data', [BookingController::class, 'getCustomerBookingsForCalendar'])->name('customer_booking_calendar_data');
        Route::get('customer_wallet', [ThemeController::class, 'customerWallet'])->name('customer_wallet');
        Route::get('favorite_professional', [CustomerController::class, 'favoriteProfessional'])->name('favorite_professional');
        Route::get('cart', [ThemeController::class, 'cart'])->name('cart');
        Route::post('remove-cart-item', [ThemeController::class, 'removeCartItem'])->name('remove_cart_item');
        Route::post('validate-coupon', [ThemeController::class, 'validateCoupon'])->name('validate_coupon');
        Route::post('store-booking-from-cart', [BookingController::class, 'storeBookingFromCart'])->name('store_booking_from_cart');
        Route::get('profile_setting', [CustomerController::class, 'profileSetting'])->name('profile_setting');
        Route::post('service-preferences/update', [CustomerController::class, 'updateServicePreferences'])->name('service-preferences.update');
        // Body specifications routes
        Route::post('body-specifications/store', [CustomerController::class, 'store'])->name('body-specifications.store');
        Route::get('body-specifications/get', [CustomerController::class, 'getBodySpecification'])->name('body-specifications.get');

        //stripe
        Route::get('/pay', [BookingController::class, 'checkout']);
        Route::get('/success', [BookingController::class, 'paymentSuccess'])->name('success');
        Route::get('/cancel', [BookingController::class, 'paymentCancel'])->name('cancel');

        // Favorite Professionals Routes
        Route::post('favorite-professionals', [CustomerController::class, 'favoriteProfessionals'])->name('favorite_professionals');
    });
});

//Businesss,individual and professional routes
Route::group(['middleware' => ['auth', 'verified', 'user_check', 'professional.restrict', 'role:admin|business|individual|professional']], function () {
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
    Route::get("booking", [BookingController::class, "dashboardBooking"])->name('booking');
    Route::get("booking/filter", [BookingController::class, "filterBookings"])->name('booking.filter');
    Route::get("booking/{ids}/{booking_number}", [BookingController::class, "showBookingDetail"])->name('booking.detail');
    Route::post("booking/update-status", [BookingController::class, "updateBookingStatus"])->name('booking.update-status');
    Route::get("booking/calendar-data", [BookingController::class, "getBusinessBookingsForCalendar"])->name('booking.calendar-data');
    Route::get("booking/export", [BookingController::class, "exportBookings"])->name('booking.export');
    Route::controller(StripeController::class)->group(function () {
        Route::post('/payment/subscription',  'purchaseSubscription')->name('payment.subscription');
        Route::get('/subscription/success',  'subscriptionSuccess')->name('subscription.success');
        Route::get('/subscription/failed',  'subscriptionFailed')->name('subscription.failed');
        Route::post('/subscription/cancel',  'cancel_subscription')->name('subscription.cancel');
    });
    //stripe connect routes
    Route::get('/connect-stripe', [StripeConnectController::class, 'connectStripe'])->name('stripe.connect');
    Route::get('/stripe/return', [StripeConnectController::class, 'handleStripeReturn'])->name('stripe.return');
    Route::get('/stripe/reauth', [StripeConnectController::class, 'connectStripe'])->name('stripe.reauth');
    Route::post('/disconnect-stripe', [StripeConnectController::class, 'disconnectStripe'])->name('stripe.disconnect');

    Route::get('setting', [DashboardController::class, 'setting'])->name('setting');
    Route::get('profile_settings', [DashboardController::class, 'profileSetting'])->name('profile_settings');
    Route::post('galleries/save', [DashboardController::class, 'saveGalleries'])->name('dashboard.galleries.save');
    Route::post('company-details/update', [DashboardController::class, 'updateCompanyDetails'])->name('company-details.update');
    Route::post('product-certifications/update', [DashboardController::class, 'updateProductCertifications'])->name('product-certifications.update');
    Route::post('certificates/save', [DashboardController::class, 'saveCertificates'])->name('certificates.save');
    Route::post('availability/save', [DashboardController::class, 'saveAvailability'])->name('availability.save');
    Route::post('intro-cards/save', [DashboardController::class, 'saveIntroCards'])->name('intro-cards.save');
    Route::resource("subscriptions", "\App\Http\Controllers\SubscriptionController");

    Route::group(['middleware' => ['role:admin|business|individual|professional']], function () {
        // Service
        Route::prefix('services')->group(function () {
            Route::get('/', [ServiceController::class, 'index'])->name('services.index');
            Route::get('/create/{type}', [ServiceController::class, 'create'])->name('services.create');
            Route::post('/store/{type}', [ServiceController::class, 'store'])->name('services.store');
            Route::get('/{service}/edit/{type}', [ServiceController::class, 'edit'])->name('services.edit');
            Route::put('/{service}/update/{type}', [ServiceController::class, 'update'])->name('services.update');
            Route::delete('/{service}/delete', [ServiceController::class, 'destroy'])->name('services.destroy');
            Route::get('/{service}', [ServiceController::class, 'show'])->name('services.show');
            Route::get('/get-subcategories/{category_id}', [ServiceController::class, 'getSubcategories'])->name('services.get-subcategories');
        });
        // Subcategory
        Route::prefix('subcategories')->group(function () {
            Route::get('/get-subcategories/{category_id}', [CategoriesController::class, 'getSubCategories'])->name('subcategories.get');
        });
        Route::resource("staffs", "\App\Http\Controllers\StaffController");
        Route::post('staffs/update-status', [StaffController::class, 'updateStatus'])->name('staffs.update-status');
        Route::get('analytics', [ThemeController::class, 'businessAnalytics'])->name('business_analytics');
        Route::get('earning', [ThemeController::class, 'businessEarning'])->name('earning');
        Route::get('earning/filter', [ThemeController::class, 'filterEarnings'])->name('earning.filter');
        Route::get('earning/export', [ThemeController::class, 'exportEarnings'])->name('earning.export');
        Route::get('staff-member-details', [ThemeController::class, 'staffMemberDetails'])->name('staff-member-details');

        // Trustpilot OAuth routes
        Route::prefix('trustpilot')->group(function () {
            Route::get('connect', [TrustpilotController::class, 'connect'])->name('trustpilot.connect');
            Route::post('disconnect', [TrustpilotController::class, 'disconnect'])->name('trustpilot.disconnect');
        });
    });
});

// Trustpilot OAuth callback (outside middleware to handle redirects)
Route::get('trustpilot/callback', [TrustpilotController::class, 'callback'])->name('trustpilot.callback');

// admin routes
Route::group(['middleware' => ['auth', 'role:admin', 'verified']], function () {
    Route::prefix('admin')->group(function () {
        //admin Professional Routes
        Route::prefix('professionals')->group(function () {
            Route::get("/", [ProfessionalController::class, 'index'])->name('professionals');
            Route::get('show/{id}', [ProfessionalController::class, 'show'])->name('professional.show');
            Route::get('approve/{id}', [ProfessionalController::class, 'approve'])->name('professional.approve');
            Route::get('change_status/{id}', [ProfessionalController::class, 'changeStatus'])->name('professional.change_status');
            Route::get('export/approved', [ProfessionalController::class, 'exportApproved'])->name('professionals.export.approved');
            Route::get('export/unapproved', [ProfessionalController::class, 'exportUnapproved'])->name('professionals.export.unapproved');
            Route::get('export/all', [ProfessionalController::class, 'exportAll'])->name('professionals.export.all');
            Route::get('filter', [ProfessionalController::class, 'filterProfessionals'])->name('professionals.filter');
            // Route::get('reject/{id}', [ProfessionalController::class, 'reject'])->name('professional.reject');
        });

        //admin Customers Routes
        Route::prefix('customers')->group(function () {
            Route::get('/', [DashboardController::class, 'adminCustomers'])->name('customers');
            Route::get('change_status/{id}', [DashboardController::class, 'changeStatus'])->name('customer.change_status');
            Route::get('export', [DashboardController::class, 'exportCustomers'])->name('customers.export');
            Route::get('show/{id}', [DashboardController::class, 'showCustomer'])->name('customer.show');
            Route::get('filter', [DashboardController::class, 'filterCustomers'])->name('customers.filter');
        });
        //admin CMS Routes
        Route::prefix('cms')->group(function () {
            Route::get('/', [CmsController::class, 'home'])->name('cms.home');
            Route::post('home', [CmsController::class, 'editHome'])->name('cms.home.edit');
            Route::get('privacy', [CmsController::class, 'privacy'])->name('cms.privacy');
            Route::get('terms', [CmsController::class, 'terms'])->name('cms.terms');
            Route::post('privacy-terms', [CmsController::class, 'editPrivacyTerms'])->name('cms.privacy-terms.edit');
            Route::get('create', [CmsController::class, 'create'])->name('cms.create');
            Route::post('store', [CmsController::class, 'store'])->name('cms.store');
            Route::post('update/{id}', [CmsController::class, 'update'])->name('cms.update');
            Route::get('{slug}', [CmsController::class, 'show'])->name('cms.show');
        });
        //admin Categories & Subcategories Routes
        Route::resource("categories", "\App\Http\Controllers\CategoriesController");
        Route::resource("subcategories", "\App\Http\Controllers\SubCategoriesController");
        Route::post('/categories/update-status', [CategoriesController::class, 'updateStatus'])->name('categories.update-status');
        Route::post('/subcategories/update-status', [SubCategoriesController::class, 'subUpdateStatus'])->name('subcategories.update-status');
        Route::get('/categories-search', [CategoriesController::class, 'search'])->name('categories.search');
        //Admin Certifications routes
        Route::get('certifications/load-more', [\App\Http\Controllers\CertificationsController::class, 'loadMore'])->name('certifications.load-more');
        Route::resource("certifications", "\App\Http\Controllers\CertificationsController");
        //Admin Holiday routes
        Route::get('holidays/load-more', [\App\Http\Controllers\HolidaysController::class, 'loadMore'])->name('holidays.load-more');
        Route::post('holidays/import', [\App\Http\Controllers\HolidaysController::class, 'import'])->name('holidays.import');
        Route::get('holidays/download-template', [\App\Http\Controllers\HolidaysController::class, 'downloadTemplate'])->name('holidays.download-template');
        Route::resource("holidays", "\App\Http\Controllers\HolidaysController");
        //Admin VAT management routes
        Route::get('vatmanagements/load-more', [\App\Http\Controllers\VatManagementsController::class, 'loadMore'])->name('vatmanagements.load-more');
        Route::resource("vatmanagements", "\App\Http\Controllers\VatManagementsController");
        //Admin Discount coupons routes
        Route::get('discount-coupons/filter', [DiscountCouponsController::class, 'filterDiscountCoupons'])->name('discount-coupons.filter');
        Route::post('discount-coupons/check-coupon-code', [DiscountCouponsController::class, 'checkCouponCode'])->name('discountcoupons.check-coupon-code');
        Route::post('discount-coupons/update-status', [DiscountCouponsController::class, 'updateStatus'])->name('discount-coupons.update-status');

        Route::get('refund_request', [ThemeController::class, 'refundRequest'])->name('refund_request');
        Route::get('wallet', [ThemeController::class, 'adminWallet'])->name('wallet');
        Route::resource("discount-coupons", "\App\Http\Controllers\DiscountCouponsController");
        //Admin Countries routes
        Route::get('countries/load-more', [\App\Http\Controllers\CountriesController::class, 'loadMore'])->name('countries.load-more');
        Route::resource("countries", "\App\Http\Controllers\CountriesController");
        Route::resource("settings", "\App\Http\Controllers\SettingsController");

        Route::resource("social-platforms", "\App\Http\Controllers\SocialPlatformController");
        Route::resource("navigation-links", "\App\Http\Controllers\NavigationLinksController");
        Route::post("navigation-links/{id}/toggle-status", "\App\Http\Controllers\NavigationLinksController@toggleStatus")->name('navigation-links.toggle-status');
    });
});

//Stepper form route
Route::get('professional_account', [ThemeController::class, 'professional_account'])->name('professional_account_stepper');
Route::get('customer/testing', [ThemeController::class, 'testing'])->name('customer.testing');

Route::get('/auth/apple', [SocialAuthController::class, 'redirectToApple'])->name('apple.login');
Route::get('/auth/apple/callback', [SocialAuthController::class, 'handleAppleCallback']);
// Google Calendar Routes
Route::get('/google-calendar/connect', [GoogleCalendarController::class, 'redirectToGoogle'])->name('google.calendar.connect');
Route::get('/google-calendar/callback', [GoogleCalendarController::class, 'handleGoogleCallback']);
//Stripe Webhook
Route::post('/stripe/webhook', [StripeController::class, 'handleStripeWebhook'])->name('stripe.webhook');
Route::post('/stripe-connect/webhook', [StripeConnectController::class, 'handle']);
Route::post('/save-booking-details', [BookingController::class, 'saveBookingDetails'])->name('saveBookingDetails');

// Test route to check booking services
Route::get('/test-booking-services/{booking_id}', function ($booking_id) {
    $booking = \App\Models\Booking::find($booking_id);
    if (!$booking) {
        return response()->json(['error' => 'Booking not found'], 404);
    }

    return response()->json([
        'booking_id' => $booking->id,
        'booking_services' => $booking->bookingServices()->with(['staff', 'user'])->get(),
        'selected_professionals' => $booking->selected_professionals
    ]);
})->name('test.booking.services');

Route::get("complete-booking-cron", [CronController::class, 'complete_booking']);
