<div class="col-md-12">
    <a class="form-control form-inputs-field d-flex justify-content-between" data-bs-toggle="modal"
        data-bs-target="#availabilityModal">Select
        availability<span><i class="fa-solid fa-chevron-down"></i></span></a>
</div>

{{-- availability modal --}}
<div class="modal fade service-availibility-calendar" id="availabilityModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content p-3">
            <div class="modal-header">
                <h5 class="modal-title">Availability</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>

            <div class="modal-body">
                <div class="d-flex justify-content-between align-items-center gap-3 mb-6">
                    <button type="button" class="btn btn-sm btn-outline-secondary" id="prevWeek">
                        &lt;
                    </button>
                    <strong id="weekRange" class="fs-18 bold">14 April 2025 - 20 April 2025</strong>
                    <button type="button" class="btn btn-sm btn-outline-secondary" id="nextWeek">
                        &gt;
                    </button>
                </div>

                <div id="weekDaysContainer"></div>

                <!-- 📝 TEXTAREA TO SHOW JSON OUTPUT -->

                <div class="d-flex justify-content-between  mt-8">
                    <label class="styled-checkbox d-flex  gap-1 w-150px" for="recurring">
                        <input type="checkbox" class="day-toggle" id="recurring">
                        <span class=" fs-14 semi-bold black w-100 text-start mb-1">Recurring</span>
                    </label>

                    <div class="Recurring-holidays d-none" id="recurringSection">
                        <div class="d-none">
                            <label for="jsonOutput" class="form-label"><strong>Selected Availability (JSON
                                    Format):</strong></label>
                            <textarea id="jsonOutput" class="form-control" rows="8" readonly name="availabilities_dates"
                                placeholder="Your selected availability will appear here in JSON format..."></textarea>
                        </div>

                        <div class="d-flex flex-end">
                            <label class="mb-0"><input type="radio" name="recurring" value="4" /> 4 Weeks</label>
                            <label class="ms-3 mb-0"><input type="radio" name="recurring" value="12" /> 12
                                Weeks</label>
                            <label class="ms-3 mb-0"><input type="radio" name="recurring" value="custom" />
                                Custom</label>
                        </div>

                        <!-- Custom Weeks Input -->
                        <div class="custom-weeks-input my-6" style="display: none;">
                            <label for="customWeeks">Enter number of weeks:</label>
                            <input type="number" id="customWeeks" class="form-control" min="1" />
                            <button type="button" class="blue-btn mt-5" id="customDone">Done</button>
                        </div>
                    </div>
                </div>

            </div>

            <div class="modal-footer">
                <button class="cancel-btn" data-bs-dismiss="modal">
                    Cancel
                </button>
                <button type="button" class="blue-btn" id="saveAvailability">Done</button>
            </div>
        </div>
    </div>
</div>
{{-- availability modal end --}}


@push('js')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>


    <script>
        $(document).ready(function () {
            $('#recurring').on('change', function () {
                if ($(this).is(':checked')) {
                    $('.Recurring-holidays').removeClass('d-none');
                } else {
                    $('.Recurring-holidays').addClass('d-none');
                }
            });

            // Prevent parent form validation when interacting with modal elements
            $('#availabilityModal').on('click change keyup', function(e) {
                e.stopPropagation();
                // Prevent any validation events from bubbling up to parent form
                if ($(e.target).closest('#individualServiceForm').length) {
                    e.preventDefault();
                }
            });

            // Ensure modal interactions don't trigger form validation
            $('#availabilityModal input, #availabilityModal button, #availabilityModal select').on('focus blur change keyup', function(e) {
                e.stopPropagation();
            });
        });
    </script>



    <script>
        const weekData = {};
        let currentWeekIndex = 0;
        const baseStartDate = moment().startOf('isoWeek'); // This will set the start date to the current week's Monday

        // 🎯 JSON DATA INITIALIZATION - Pass your array JSON data here
        const initializeDataFromJSON = () => {
            // 📋 YOUR JSON DATA - Replace this array with your actual API response
            // const availabilityArray = @js(old("availabilities_dates", $availabilities ?? []));
            const availabilityArray = @js($availabilities ?? []);

            console.log("Original array data:", availabilityArray);

            if (!availabilityArray.length) {
                return;
            }

            // Convert array format to week-based format for the calendar
            availabilityArray.forEach(item => {
                const date = moment(item.date);
                const today = moment().startOf('day');
                const currentWeekStart = moment().startOf('isoWeek');

                // 🚫 SKIP PAST DATES - Only skip dates from previous weeks, not past dates in current week
                if (date.isBefore(currentWeekStart, 'day')) {
                    console.log(`Skipping past week date: ${item.date} (${item.day})`);
                    return; // Skip this iteration for dates from previous weeks
                }

                const weekStart = date.clone().startOf('isoWeek'); // Get Monday of that week
                const weekKey = weekStart.format("YYYY-MM-DD");
                const dayName = item.day;

                // Convert time format from "HH:MM:SS" to "HH:MM"
                const startTime = item.start_time.substring(0, 5); // Remove seconds
                const endTime = item.end_time.substring(0, 5); // Remove seconds

                // Initialize week if it doesn't exist
                if (!weekData[weekKey]) {
                    weekData[weekKey] = {
                        Monday: {
                            enabled: false,
                            start: "10:00",
                            end: "19:00"
                        },
                        Tuesday: {
                            enabled: false,
                            start: "10:00",
                            end: "19:00"
                        },
                        Wednesday: {
                            enabled: false,
                            start: "10:00",
                            end: "19:00"
                        },
                        Thursday: {
                            enabled: false,
                            start: "10:00",
                            end: "19:00"
                        },
                        Friday: {
                            enabled: false,
                            start: "10:00",
                            end: "19:00"
                        },
                        Saturday: {
                            enabled: false,
                            start: "10:00",
                            end: "19:00"
                        },
                        Sunday: {
                            enabled: false,
                            start: "10:00",
                            end: "19:00"
                        }
                    };
                }

                // Set the specific day data (only for current and future dates)
                weekData[weekKey][dayName] = {
                    enabled: true,
                    start: startTime,
                    end: endTime,
                    id: item.id, // Store original ID for reference
                    service_id: item.service_id // Store service_id for reference
                };
            });

            console.log("Data loaded from JSON array:", weekData);
            console.log("Original array data:", availabilityArray);
        };

        // 🎯 EXTRACT SELECTED AVAILABILITY IN YOUR DESIRED FORMAT
        const getSelectedAvailability = () => {
            const selectedAvailability = [];

            // Loop through all weeks in weekData
            Object.keys(weekData).forEach(weekKey => {
                const weekStart = moment(weekKey); // Monday of the week
                const weekDays = weekData[weekKey];

                // Check each day of the week
                Object.keys(weekDays).forEach(dayName => {
                    const dayData = weekDays[dayName];

                    // Only include enabled days
                    if (dayData.enabled) {
                        // Calculate the actual date for this day
                        const dayIndex = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday",
                            "Saturday", "Sunday"
                        ].indexOf(dayName);
                        const actualDate = weekStart.clone().add(dayIndex, 'days');

                        // Add to result array in your desired format
                        selectedAvailability.push({
                            "date": actualDate.format("YYYY-MM-DD"),
                            "day": dayName,
                            "start": dayData.start,
                            "end": dayData.end
                        });
                    }
                });
            });

            // Sort by date for better organization
            selectedAvailability.sort((a, b) => moment(a.date).diff(moment(b.date)));

            return selectedAvailability;
        };

        // 📝 UPDATE TEXTAREA WITH JSON OUTPUT
        const updateJsonOutput = () => {
            const selectedData = getSelectedAvailability();
            const jsonString = JSON.stringify(selectedData, null, 2);
            $("#jsonOutput").val(jsonString);
        };

        const updateWeekUI = () => {
            const startOfWeek = baseStartDate.clone().add(currentWeekIndex * 7, "days");
            const weekDays = Array.from({
                length: 7
            }, (_, i) => startOfWeek.clone().add(i, "days"));
            const weekRange = `${weekDays[0].format("DD MMM YYYY")} - ${weekDays[6].format("DD MMM YYYY")}`;
            const weekKey = startOfWeek.format("YYYY-MM-DD");
            const week = weekData[weekKey] || {};

            $("#weekRange").text(weekRange);

            // Update previous week button state
            if (currentWeekIndex <= 0) {
                $("#prevWeek").prop('disabled', true).addClass('disabled');
            } else {
                $("#prevWeek").prop('disabled', false).removeClass('disabled');
            }

            const dayNames = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"];
            const today = moment().startOf('day');

            const html = dayNames.map((day, index) => {
                const date = weekDays[index];
                const formattedDate = date.format("YYYY-MM-DD");
                const val = week[day] || {
                    start: "10:00",
                    end: "19:00",
                    enabled: false
                };

                // Only disable dates if we're viewing a previous week (currentWeekIndex < 0)
                // For current week (currentWeekIndex = 0) and future weeks, all dates are enabled
                const isPastWeek = currentWeekIndex < 0;

                return `
                                <div class="d-flex justify-content-between align-items-center day-row" data-day="${day}" data-date="${formattedDate}">
                                    <div>
                                        <input type="checkbox" class="form-check-input me-2 day-checkbox" data-day="${day}"
                                                ${val.enabled ? "checked" : ""} ${isPastWeek ? "disabled" : ""}>
                                        <label class="me-2 ${isPastWeek ? 'text-muted' : ''}" style="width: 100px;">${day}</label>
                                    </div>
                                    ${val.enabled ? `


                                    <div class="d-flex align-items-center">
                                         <input type="time" class="form-control form-control-sm me-2 start-time" value="${val.start}" data-day="${day}" style="width:120px;">
                                    <span class="me-2 w-20px">to</span>
                                    <input type="time" class="form-control form-control-sm end-time" value="${val.end}" data-day="${day}" style="width:120px;">
                                ` : `<span class="text-muted">Closed</span>`}
                                    </div>

                                </div>`;
            }).join("");

            $("#weekDaysContainer").html(html);
        };

        const saveCurrentWeekData = () => {
            const base = baseStartDate.clone().add(currentWeekIndex * 7, "days");
            const key = base.format("YYYY-MM-DD");
            weekData[key] = {};

            $(".day-row").each(function () {
                const day = $(this).data("day");
                const enabled = $(this).find(".day-checkbox").is(":checked");
                const start = $(this).find(".start-time").val() || "10:00";
                const end = $(this).find(".end-time").val() || "19:00";
                weekData[key][day] = {
                    enabled,
                    start,
                    end
                };
            });
        };

        // Function to duplicate the weeks (with reset functionality)
        const duplicateWeeks = (weeks) => {
            const current = baseStartDate.clone().add(currentWeekIndex * 7, "days");
            const srcKey = current.format("YYYY-MM-DD");

            // 🔄 RESET: Clear all future week duplications first
            Object.keys(weekData).forEach(weekKey => {
                const weekDate = moment(weekKey);
                const currentWeekDate = moment(srcKey);

                // Remove any week that's after the current week and was previously duplicated
                if (weekDate.isAfter(currentWeekDate, 'week')) {
                    // Check if this week has the same pattern as current week (indicating it was duplicated)
                    const currentWeekData = weekData[srcKey];
                    const weekToCheck = weekData[weekKey];

                    // Only remove if it looks like a duplication (same enabled pattern)
                    if (currentWeekData && weekToCheck) {
                        const currentEnabledDays = Object.keys(currentWeekData).filter(day => currentWeekData[
                            day].enabled);
                        const checkEnabledDays = Object.keys(weekToCheck).filter(day => weekToCheck[day]
                            .enabled);

                        // If same number of enabled days, likely a duplication - remove it
                        if (currentEnabledDays.length === checkEnabledDays.length && currentEnabledDays.length >
                            0) {
                            delete weekData[weekKey];
                        }
                    }
                }
            });

            // 📅 CREATE: Now create fresh duplications for the selected weeks
            for (let i = 1; i < weeks; i++) {
                const next = current.clone().add(i * 7, "days");
                const newKey = next.format("YYYY-MM-DD");

                // Create deep copy of current week's data
                weekData[newKey] = JSON.parse(JSON.stringify(weekData[srcKey]));
            }

            console.log(`Reset and duplicated for ${weeks} weeks from ${srcKey}`);
        };

        // Validate time inputs
        const validateTimeInput = (input) => {
            const $input = $(input);
            const startTime = $input.val();
            const endTimeInput = $input.hasClass('start-time') ?
                $input.closest('.day-row').find('.end-time') :
                $input.closest('.day-row').find('.start-time');
            const endTime = endTimeInput.val();

            // Basic validation to ensure start time is before end time
            if (startTime && endTime && startTime >= endTime) {
                alert('Start time must be before end time');
                $input.focus();
            }
        };

        $(document).ready(function () {
            // Initialize data from JSON - THIS IS WHERE YOU PASS YOUR JSON DATA
            initializeDataFromJSON();

            updateWeekUI();
            updateJsonOutput(); // Initialize JSON output

            $(document).on("change", ".day-checkbox", function () {
                saveCurrentWeekData();
                updateWeekUI();
                updateJsonOutput(); // Update JSON when checkbox changes
            });

            // Validate time inputs when they change
            $(document).on("change", ".start-time, .end-time", function () {
                validateTimeInput(this);
                saveCurrentWeekData();
                updateJsonOutput(); // Update JSON when time changes
            });

            $("#prevWeek").click(function (e) {
                e.preventDefault();
                e.stopPropagation();

                // Prevent going to previous weeks (only allow current week and future weeks)
                if (currentWeekIndex <= 0) {
                    return; // Don't allow going to previous weeks
                }

                saveCurrentWeekData();
                currentWeekIndex--;
                updateWeekUI();
                updateJsonOutput(); // Update JSON when week changes
            });

            $("#nextWeek").click(function (e) {
                e.preventDefault();
                e.stopPropagation();

                saveCurrentWeekData();
                currentWeekIndex++;
                updateWeekUI();
                updateJsonOutput(); // Update JSON when week changes
            });

            $("#saveAvailability").click(function (e) {
                e.preventDefault();
                e.stopPropagation();

                saveCurrentWeekData();
                updateJsonOutput(); // Final update of JSON

                const selectedData = getSelectedAvailability();

                // Close the modal
                $('#availabilityModal').modal('hide');

                console.log("📅 Selected Availability Array:", selectedData);
                console.log("📋 Full week data structure:", weekData);
            });

            // Recurring Radio Button Change
            $("input[name='recurring']").change(function (e) {
                e.stopPropagation();

                const selected = $(this).val();
                saveCurrentWeekData();

                if (selected === "custom") {
                    $(".custom-weeks-input").show();
                } else {
                    $(".custom-weeks-input").hide();
                }

                if (selected === "custom") {
                    return false;
                }

                const repeatWeeks = parseInt(selected);
                console.log("repeatWeeks here", repeatWeeks);

                if (repeatWeeks > 0) {
                    duplicateWeeks(repeatWeeks);
                    updateJsonOutput(); // Update JSON after duplication
                    alert(`Availability reset and duplicated for ${repeatWeeks} weeks total.`);
                }
            });

            // Handle Custom Weeks Input
            $("#customDone").click(function (e) {
                e.preventDefault();
                e.stopPropagation();

                const customWeeks = parseInt($("#customWeeks").val());
                if (!isNaN(customWeeks) && customWeeks > 0) {
                    saveCurrentWeekData();
                    duplicateWeeks(customWeeks);
                    updateJsonOutput(); // Update JSON after custom duplication
                    alert(`Availability reset and duplicated for ${customWeeks} weeks total.`);
                    $(".custom-weeks-input").hide();
                    $("input[name='recurring']").prop('checked', false); // Uncheck radio buttons
                } else {
                    alert("Please enter a valid number of weeks.");
                }
            });

            // Expose availability data globally for form submission
            window.getAvailabilityData = function() {
                saveCurrentWeekData();
                return getSelectedAvailability();
            };
        });
    </script>
@endpush