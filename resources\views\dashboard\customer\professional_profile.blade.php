@extends(auth()->check() ? (auth()->user()->hasRole('customer') ? 'website.layout.master' : 'dashboard.layout.master') : 'website.layout.master')
@push('css')
    <style>
        .cursor-pointer {
            cursor: pointer;
        }

        #map {
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            transition: box-shadow 0.3s ease;
        }

        #map:hover {
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        }

        .favorite-icon {
            cursor: pointer;
        }

        .favorite-icon i {
            font-size: 1.5rem;
        }

        .favorite-icon i.fas.text-danger {
            color: #e91e63 !important;
        }
    </style>
@endpush

@section('content')
    <div id="kt_app_content" class="app-content flex-column-fluid customer_dashboard bg-color addfamily padding-block">
        <div id="kt_app_content_container" class="container py-5 px-5">
            <div class="row row-gap-5">
                <div class="col-md-12 d-flex gap-8 align-items-center ms-0 px-0">
                    <div class="pro-stylist-logo">
                        <img src="{{ asset('website') . '/' . $user->profile->pic ?? '' }}">
                    </div>

                    <div class="w-100">
                        <h5>{{ $user->name ?? '' }} <span class="top-rated ms-5">
                                <img src="{{ asset('website') }}/assets/images/top-rated-star.png" class="img-fluid"> TOP
                                RATED </span> </h5>

                        <div class="d-flex align-items-center">
                            <p class="fs-16 semi-bold dark-blue"><span><i class="fas fa-star pe-3"></i></span>5.0
                                <span>(546)</span>
                            </p>
                            <ul class="d-flex gap-12 sub-details">
                                {{-- <li class="fs-16">Open until 11:00 pm</li> --}}
                                @if ($user->profile->city && $user->profile->country)
                                    <li class="fs-16">{{ $user->profile->city ?? '' }},{{ $user->profile->country ?? '' }}
                                    </li>
                                @endif
                                <li class="fs-16"><a href="#directions"><span class="fs-14 ms-5 deep-blue"> <i
                                                class="fas fa-external-link-alt fa-4"> </i> Get Directions </span></a>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div class="d-flex flex-end w-50 align-items-center">
                        <button style="all: unset" id="generateShortUrlBtn" data-user-id="{{ $user->ids }}">
                            <img src="{{ asset('website') }}/assets/images/upload.svg">
                        </button>
                        @if (auth()->check() && auth()->user()->hasRole('customer'))
                            <button class="favorite-icon main-heading-icon" style="all: unset" data-user-id="{{ $user->id }}">
                                <i class="fa-heart fa-5 {{ $isFavorited ? 'fas text-danger' : 'far' }}" id="heart-icon"></i>
                            </button>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        @if ($user->galleries->count() > 0)
            <div class="container-fluid professional-swiper banner-swiper">
                <div class="row">
                    <div class="col-md-12">
                        <div class="swiper mySwiper">
                            <div class="swiper-wrapper">
                                @foreach ($user->galleries as $gallery)
                                    <div class="swiper-slide"><img src="{{ asset('website') . '/' . $gallery->image }}">
                                    </div>
                                @endforeach
                            </div>
                            <div class="swiper-pagination"></div>

                            <div class="swiper-button-next"></div>
                            <div class="swiper-button-prev"></div>
                            <div class="swiper-pagination"></div>
                        </div>
                    </div>
                </div>
            </div>
        @endif

        <div class="container">
            <div class="row">
                @if ($user->product_cerficates->count() > 0)
                    <div class="col-md-12 mb-10">
                        <div class="swiper mySwiper mySwiper2 certifications-logo pt-15">
                            <p class="fs-16 Sora bold">Product Certifications</p>
                            <div class="swiper-wrapper">
                                @foreach ($user->product_cerficates as $productCertification)
                                    <div class="swiper-slide">
                                        <img src="{{ asset('website') . '/' . $productCertification->image }}">
                                    </div>
                                @endforeach
                            </div>
                            <div class="swiper-button-next"></div>
                            <div class="swiper-button-prev"></div>
                            <div class="swiper-pagination"> </div>
                        </div>
                    </div>
                @endif

                @if ($user->introCards->count() > 0)
                    <div class="col-md-12 mb-10">
                        <div class="swiper mySwiper guarantee-section ">
                            <div class="swiper-wrapper">
                                @foreach ($user->introCards as $card)
                                    <div class="swiper-slide">
                                        <div class="d-flex gap-5 align-items-center cards">
                                            <img src="{{ asset('website') . '/' . $card->image }}" width="50px"
                                                height="50px">

                                            <div>
                                                <p class="fs-16 bold mb-0">{{ $card->heading ?? '' }} </p>
                                                <p class="fs-16 bold light-gray mb-0">{{ $card->description ?? '' }}</p>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>

                            <div class="swiper-button-next"></div>
                            <div class="swiper-button-prev"></div>
                            <div class="swiper-pagination"> </div>
                        </div>
                    </div>
                @endif

                <div class="col-md-8">
                    <div class="container mb-5">
                        <div class="row">
                            <div class="col-md-12">
                                <!-- Search and Title Section -->
                                <div class="d-flex justify-content-between mb-10">
                                    <h4 class="fs-24 sora black">Services</h4>
                                    <div class="search-bar d-flex align-items-center">
                                        <i class="fa-solid fa-magnifying-glass me-3"></i>
                                        <input type="text" placeholder="Search">
                                    </div>
                                </div>

                                <!-- Categories Section with Filter Button in Same Line -->
                                <div class="d-flex justify-content-between align-items-center pro-service-tabs">
                                    <ul class="nav nav-pills mb-10" id="pills-tab" role="tablist">
                                        <!-- "All" Tab (Always Active) -->
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link active" id="all-services-tab" data-bs-toggle="pill"
                                                data-bs-target="#all-services" type="button" role="tab"
                                                aria-controls="all-services" aria-selected="true">
                                                All
                                            </button>
                                        </li>

                                        <!-- Category Tabs -->
                                        @foreach ($userCategories as $index => $category)
                                            <li class="nav-item" role="presentation">
                                                <button class="nav-link category-tab-btn"
                                                    id="category-{{ $category->id }}-tab" data-bs-toggle="pill"
                                                    data-bs-target="#category-{{ $category->id }}" type="button"
                                                    role="tab" data-category-id="{{ $category->id }}"
                                                    aria-controls="category-{{ $category->id }}" aria-selected="false">
                                                    {{ $category->name }}
                                                </button>
                                            </li>
                                        @endforeach
                                    </ul>

                                    {{-- <a href="#" class="fs-14 sora normal black pt-3" data-bs-toggle="modal"
                                        data-bs-target="#professionalfilterModal">
                                        <div class="filter-select d-flex gap-2 align-items-center">
                                            @include('svg.filter')
                                            <!-- Assuming you have an SVG for the filter icon -->
                                            <span>Filter</span>
                                        </div>
                                    </a> --}}
                                </div>

                                <!-- Tab Content Section -->
                                <div class="tab-content" id="pills-tabContent">

                                    <!-- All Services Tab (Always Visible) -->
                                    <div class="tab-pane fade show active" id="all-services" role="tabpanel"
                                        aria-labelledby="all-services-tab">
                                        <div class="row row-gap-5" id="services-list">
                                            <table id="responsiveTable" class=" display wallet-history-table"
                                                style="width: 100%">
                                                <thead>
                                                    <tr>
                                                        <th></th>
                                                        <th>Duration</th>
                                                        <th>Price</th>
                                                        <th>Category</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @forelse ($user->services as $service)
                                                        <tr class="service-item" data-price="{{ $service->price }}"
                                                            data-duration="{{ $service->duration }}">
                                                            <td>
                                                                <div class="card flex-row shadow-none p-0 gap-3">
                                                                    <div
                                                                        class="card-header p-0 border-0 align-items-start">
                                                                        <img src="{{ asset('website') . '/' . $service->image }}"
                                                                            class="h-80px w-80px rounded-3 object-fit-contain"
                                                                            alt="card-image" />
                                                                    </div>
                                                                    <div class="card-body p-0">
                                                                        <p class="fs-16 regular black">
                                                                            {{ $service->name }}</p>
                                                                        <p class="light-black opacity-6 fs-14 normal">
                                                                            {{ $service->description }}</p>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td>{{ $service->duration }} minutes</td>
                                                            <td>${{ $service->price }}</td>
                                                            <td>{{ $service->category->name ?? 'N/A' }}</td>
                                                            <td data-label="" class="text-end" data-bs-toggle="modal"
                                                                data-bs-target="#service-details"> <span
                                                                    class="gray-btn rounded-1 deep-blue fs-16 add-to-cart">
                                                                    <i class="fas fa-plus"></i> Add to cart
                                                                </span>
                                                            </td>
                                                        </tr>
                                                    @empty
                                                        <tr>
                                                            <td colspan="4" class="text-center py-4">
                                                                <p class="fs-16 text-muted">No services available</p>
                                                            </td>
                                                        </tr>
                                                    @endforelse
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>

                                    <!-- Category Tabs Content -->
                                    @foreach ($userCategories as $index => $category)
                                        <div class="tab-pane fade" id="category-{{ $category->id }}" role="tabpanel"
                                            aria-labelledby="category-{{ $category->id }}-tab">
                                            <div class="d-flex justify-content-between flex-start flex-wrap">
                                                <!-- Subcategories for the specific category -->
                                                <ul class="nav nav-pills mb-10" class="pro-subcategories-tabs"
                                                    id="subcategory-pills-tab" role="tablist">
                                                    @if (isset($userSubcategoriesByCategory[$category->id]))
                                                        @foreach ($userSubcategoriesByCategory[$category->id] as $subcategory)
                                                            <li class="nav-item" role="presentation">
                                                                <button
                                                                    class="nav-link {{ $loop->first ? 'active' : '' }}"
                                                                    id="subcategory-{{ $subcategory->id }}-tab"
                                                                    data-bs-toggle="pill"
                                                                    data-bs-target="#subcategory-{{ $subcategory->id }}"
                                                                    type="button" role="tab"
                                                                    aria-controls="subcategory-{{ $subcategory->id }}"
                                                                    aria-selected="{{ $loop->first ? 'true' : 'false' }}">
                                                                    {{ $subcategory->name }}
                                                                </button>
                                                            </li>
                                                        @endforeach
                                                    @else
                                                        <li class="nav-item" role="presentation">
                                                            <button class="nav-link active" type="button">
                                                                No subcategories available
                                                            </button>
                                                        </li>
                                                    @endif
                                                </ul>
                                            </div>

                                            <!-- Services under each subcategory -->
                                            <div class="tab-content" id="subcategory-pills-tabContent">
                                                @if (isset($userSubcategoriesByCategory[$category->id]))
                                                    @foreach ($userSubcategoriesByCategory[$category->id] as $subcategory)
                                                        <div class="tab-pane fade {{ $loop->first ? 'show active' : '' }}"
                                                            id="subcategory-{{ $subcategory->id }}" role="tabpanel"
                                                            aria-labelledby="subcategory-{{ $subcategory->id }}-tab">
                                                            <div class="row row-gap-5">
                                                                <table id="responsiveTable"
                                                                    class="display wallet-history-table"
                                                                    style="width: 100%">
                                                                    <thead>
                                                                        <tr>
                                                                            <th></th>
                                                                            <th>Duration</th>
                                                                            <th>Price</th>
                                                                            <th>Category</th>
                                                                        </tr>
                                                                    </thead>
                                                                    <tbody>
                                                                        @php
                                                                            // Get only this professional's services that belong to this subcategory
$professionalServicesInSubcategory = $user->services->where(
    'subcategory_id',
                                                                                $subcategory->id,
                                                                            );
                                                                        @endphp
                                                                        @foreach ($professionalServicesInSubcategory as $service)
                                                                            <tr>
                                                                                <td>
                                                                                    <div
                                                                                        class="card flex-row shadow-none p-0 gap-3">
                                                                                        <div
                                                                                            class="card-header p-0 border-0 align-items-start">
                                                                                            <img src="{{ asset('website') }}/assets/images/service-image.png"
                                                                                                class="h-80px w-80px rounded-3 object-fit-contain"
                                                                                                alt="card-image" />
                                                                                        </div>
                                                                                        <div class="card-body p-0">
                                                                                            <p
                                                                                                class="light-black opacity-6 fs-14 normal">
                                                                                                {{ $service->description }}
                                                                                            </p>
                                                                                        </div>
                                                                                    </div>
                                                                                </td>
                                                                                <td>{{ $service->duration }} minutes</td>
                                                                                <td>${{ $service->price }}</td>
                                                                                <td>{{ $service->category->name }}</td>
                                                                                <td data-label="" class="text-end"
                                                                                    data-bs-toggle="modal"
                                                                                    data-bs-target="#service-details">
                                                                                    <span
                                                                                        class="gray-btn rounded-1 deep-blue fs-16 add-to-cart">
                                                                                        <i class="fas fa-plus"></i> Add to
                                                                                        cart
                                                                                    </span>
                                                                                </td>
                                                                            </tr>
                                                                        @endforeach
                                                                    </tbody>
                                                                </table>
                                                            </div>
                                                        </div>
                                                    @endforeach
                                                @else
                                                    <div class="tab-pane fade show active" role="tabpanel">
                                                        <div class="row row-gap-5">
                                                            <div class="col-12 text-center py-4">
                                                                <p class="fs-16 text-muted">No subcategories or services
                                                                    available for this category</p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                @endif
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="container">
                        <div class="row">
                            <div class="col-md-12 mb-15">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h5>Reviews</h5>
                                        <p class="black"> <i class="fas fa-star black"></i> 5.0 (546)</p>
                                    </div>
                                    {{-- <a href="#!" class="black-border-btn"> See All</a> --}}
                                </div>
                                <div class="swiper mySwiper review-swiper">
                                    <div class="swiper-wrapper">
                                        @for ($i = 0; $i <= 6; $i++)
                                            <div class="swiper-slide">
                                                <div class="guarantee-section rounded-4 p-10">
                                                    <div class="d-flex" style="gap: 10px;">
                                                        <img src="{{ asset('website') }}/assets/images/family4.png"
                                                            alt="Reviewer Photo"
                                                            style="width: 48px; height: 48px; border-radius: 50%;">
                                                        <div>
                                                            <p class="fs-15 Sora bold mb-0">Charlie Culhane</p>
                                                            <p class="dark-cool-gray fs-14">Tue, 25 Mar 2025 at 5:12 pm</p>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <p class="fs-14">
                                                            Lorem ipsum dolor sit amet consectetur. Enim fames a pharetra
                                                            congue
                                                            non amet.
                                                            Amet ut a tellus ipsum volutpat eget velit nulla ridiculus. Eu
                                                            nunc
                                                            at aliquam
                                                            lorem leo sit.
                                                        </p>
                                                        <div>
                                                            <span> <i class="fas fa-star"></i> <i class="fas fa-star"></i>
                                                                <i class="fas fa-star"></i> <i class="fas fa-star"></i> <i
                                                                    class="fas fa-star"></i></span> <span
                                                                class="fs-14 bold">5.0</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        @endfor
                                        <!-- Add more .swiper-slide items here for additional reviews -->
                                    </div>
                                    <!-- Navigation buttons -->
                                    <div class="swiper-button-next"></div>
                                    <div class="swiper-button-prev"></div>
                                </div>
                            </div>

                            @if ($user->staffs->count() > 0)
                                <div class="col-md-12 mb-15">
                                    <div class="d-flex justify-content-between align-items-center mb-10">
                                        <h5>Meet The Team</h5>
                                        {{-- <a href="#!" class="black-border-btn"> See All</a> --}}
                                    </div>

                                    <div class="swiper mySwiper meet-the-team-swiper">
                                        <div class="swiper-wrapper">
                                            @foreach ($user->staffs as $staff)
                                                <div class="swiper-slide">
                                                    <div class="guarantee-section p-5">
                                                        <div class="d-flex flex-column justify-content-center align-items-center"
                                                            style="gap: 10px;">
                                                            <img src="{{ asset('website') . '/' . $staff->image }}"
                                                                alt="Reviewer Photo">
                                                            <p class="fs-16 bold mb-2">{{ $staff->name ?? '' }}</p>

                                                            <ul class="pro-social-icons">
                                                                <li> <img
                                                                        src="http://127.0.0.1:8000/website/assets/images/facebook-original.svg"
                                                                        class="img-fluid " alt="card-image"> </li>
                                                                <li> <img
                                                                        src="http://127.0.0.1:8000/website/assets/images/instagram-original.svg"
                                                                        class="img-fluid " alt="card-image"> </li>
                                                                <li> <img
                                                                        src="http://127.0.0.1:8000/website/assets/images/twitter-original.svg"
                                                                        class="img-fluid " alt="card-image"> </li>
                                                                <li> <img
                                                                        src="http://127.0.0.1:8000/website/assets/images/tiktok-original.svg"
                                                                        class="img-fluid " alt="card-image"> </li>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                </div>
                                            @endforeach
                                            <!-- Add more .swiper-slide items here for additional reviews -->
                                        </div>

                                        <!-- Navigation buttons -->
                                        <div class="swiper-button-next"></div>
                                        <div class="swiper-button-prev"></div>
                                    </div>


                                </div>
                            @endif

                            {{-- <div class="col-md-12 mb-15">
                                <div class="d-flex justify-content-between align-items-center mb-10">
                                    <h5>Portfolio</h5>
                                    <a href="#!" class="black-border-btn"> See All</a>
                                </div>

                                <div class="professional-image-gallery gallery" id="gallery">
                                    <!-- First 6 images -->
                                    <div class="image-card"><img
                                            src="{{ asset('website') }}/assets/images/portfolio1.png" alt="portfolio">
                                    </div>
                                    <div class="image-card"><img
                                            src="{{ asset('website') }}/assets/images/portfolio2.png" alt="portfolio">
                                    </div>
                                    <div class="image-card"><img
                                            src="{{ asset('website') }}/assets/images/portfolio3.png" alt="portfolio">
                                    </div>
                                    <div class="image-card"><img
                                            src="{{ asset('website') }}/assets/images/portfolio4.png" alt="portfolio">
                                    </div>
                                    <div class="image-card"><img
                                            src="{{ asset('website') }}/assets/images/portfolio1.png" alt="portfolio">
                                    </div>

                                    <!-- 6th image with overlay -->
                                    <div class="image-card" onclick="showMoreImages()">
                                        <img src="{{ asset('website') }}/assets/images/portfolio1.png" alt="portfolio">
                                        <div class="overlay">25+</div>
                                    </div>

                                    <!-- Hidden images -->
                                    <div class="image-card hidden-image"><img
                                            src="{{ asset('website') }}/assets/images/portfolio1.png" alt="portfolio">
                                    </div>
                                    <div class="image-card hidden-image"><img
                                            src="{{ asset('website') }}/assets/images/portfolio2.png" alt="portfolio">
                                    </div>
                                    <div class="image-card hidden-image"><img
                                            src="{{ asset('website') }}/assets/images/portfolio3.png" alt="portfolio">
                                    </div>
                                    <div class="image-card hidden-image"><img
                                            src="{{ asset('website') }}/assets/images/portfolio4.png" alt="portfolio">
                                    </div>
                                    <div class="image-card hidden-image"><img
                                            src="{{ asset('website') }}/assets/images/portfolio1.png" alt="portfolio">
                                    </div>
                                    <div class="image-card hidden-image"><img
                                            src="{{ asset('website') }}/assets/images/portfolio2.png" alt="portfolio">
                                    </div>
                                </div>
                            </div> --}}

                            @if ($user->certificates->count() > 0)
                                <div class="col-md-12 mb-10">
                                    <div class="d-flex justify-content-between align-items-center mb-10">
                                        <h5 class="fs-24">Certifications & Licenses</h5>
                                        {{-- <a href="#!" class="black-border-btn"> See All</a> --}}
                                    </div>

                                    <div class="swiper cert-swiper">
                                        <div class="swiper-wrapper">
                                            @foreach ($user->certificates as $certificate)
                                                <div class="swiper-slide">
                                                    <div class="guarantee-section p-5 ">
                                                        <div class=" certificate-license-swiper gap-5">
                                                            <img src="{{ asset('website') . '/' . $certificate->image }}"
                                                                alt="Reviewer Photo" s>

                                                            <p class="fs-14 bold mb-2"> {{ $certificate->name ?? '' }}
                                                            </p>
                                                            <p class="link-gray fs-14 mb-2">Issued by: <span
                                                                    class="black fs-14">
                                                                    {{ $certificate->issued_by ?? '' }}</span></p>
                                                            <p class="link-gray fs-14 mb-2">Issue Date: <span
                                                                    class="black fs-14">
                                                                    {{ $certificate->issued_date ?? '' }}</span></p>
                                                            <p class="link-gray fs-14"> Expiry Date:<span
                                                                    class="black fs-14">
                                                                    {{ $certificate->end_date ?? '' }}</span></p>
                                                        </div>
                                                    </div>
                                                </div>
                                            @endforeach
                                        </div>
                                        <div class="swiper-button-next"></div>
                                        <div class="swiper-button-prev"></div>
                                    </div>
                                </div>
                            @endif

                            @if ($user->profile && $user->profile->lat && $user->profile->lng)
                                <div class="col-md-12 mb-10" id="directions">
                                    <h5 class="fs-24 mb-8">Where to find us</h5>
                                    <!-- Google Map using existing include -->
                                    <div id="map" style="width:100%; height: 500px; border-radius: 8px;"></div>
                                    <!-- Hidden inputs for the map include -->
                                    <input type="hidden" id="pac-input" value="{{ $user->profile->location }}"
                                        style="display: none;">
                                    <input type="hidden" id="latitude" value="{{ $user->profile->lat }}">
                                    <input type="hidden" id="longitude" value="{{ $user->profile->lng }}">
                                    <p class="fs-14 light-black pt-5">
                                        {{ $user->profile->location ?? ($user->profile->city . ', ' . $user->profile->country ?? 'Location not specified') }}
                                        <span class="fs-14 ms-5 deep-blue cursor-pointer"
                                            onclick="getDirections({{ $user->profile->lat }}, {{ $user->profile->lng }})">
                                            <i class="fas fa-external-link-alt "> </i> Get Directions
                                        </span>
                                    </p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                <div class="col-md-4 time-container">
                    <div class="guarantee-section mb-10 px-5 pt-10">
                        @if (auth()->check() && auth()->user()->hasRole('customer'))
                            <a href="{{ route('chats.index', ['professional_id' => $user->ids]) }}" class="blue-button d-block text-center">
                                <span> <i class="bi bi-chat-left-text text-chat"></i> </span> Message
                            </a>
                        @else
                            <a href="#!" class="blue-button d-block text-center">
                                <span> <i class="bi bi-chat-left-text text-chat"></i> </span> Message
                            </a>
                        @endif
                        <div class="hours-container mt-10">
                            <div class="hours-toggle"
                                onclick="document.querySelector('.hours-list').classList.toggle('active')">
                                <i class="far fa-clock large-icon"></i>
                                <span class="fs-14 Sora">Open until</span>
                                <span class="chevron"> <i class="fas fa-angle-up"></i></span>
                            </div>
                            <ul class="hours-list ">
                                @foreach ($user->allOpeningHours as $openingHour)
                                    <li class="hours-row">
                                        <span class="day"><i class="dot">●</i> {{ $openingHour->day }}</span>
                                        @if ($openingHour->open && $openingHour->close)
                                            <span class="time">{{ $openingHour->open ?? 'Closed' }} –
                                                {{ $openingHour->close ?? 'Closed' }}</span>
                                        @else
                                            <span class="time">Closed</span>
                                        @endif
                                    </li>
                                @endforeach
                            </ul>

                            <div class="pt-6">
                                <p class="fs-14 Sora light-black"> <span> <img
                                            src="{{ asset('website') }}/assets/images/phone-call.svg" class="img-fluid "
                                            alt="card-image"> </span> {{ $user->profile->phone ?? '' }} </p>
                                @if ($user->profile->city && $user->profile->country)
                                    <p class="fs-14 Sora light-black"> <span> <i class="bi bi-geo-alt large-icon"></i>
                                        </span>
                                        {{ $user->profile->city ?? '' }} , {{ $user->profile->country ?? '' }} </p>
                                @endif
                                {{-- <p class="fs-14 Sora deep-blue ms-1"> <span> <i
                                            class="fas fa-external-link-alt deep-blue"></i> </span> Get directions </p> --}}

                                <ul class="service-social-icons">
                                    <li> <img src="{{ asset('website') }}/assets/images/facebook-original.svg"
                                            class="img-fluid " alt="card-image"> </li>
                                    <li> <img src="{{ asset('website') }}/assets/images/instagram-original.svg"
                                            class="img-fluid " alt="card-image"> </li>
                                    <li> <img src="{{ asset('website') }}/assets/images/twitter-original.svg"
                                            class="img-fluid " alt="card-image"> </li>
                                    <li> <img src="{{ asset('website') }}/assets/images/tiktok-original.svg"
                                            class="img-fluid " alt="card-image"> </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @include('dashboard.templates.modal.add-service-details-modal')
        @include('dashboard.templates.modal.professional-services-filter')
        @include('dashboard.templates.modal.professional-service-category-modal')
    @endsection

    @push('js')
        <script>
            var swiper = new Swiper(".review-swiper", {
                slidesPerView: 2,
                spaceBetween: 15,
                freeMode: true,
                loop: true,
                navigation: {
                    nextEl: ".swiper-button-next",
                    prevEl: ".swiper-button-prev",
                },
            });
        </script>

        <script>
            var swiper = new Swiper(".cert-swiper", {
                slidesPerView: 4,
                spaceBetween: 10,
                freeMode: true,
                loop: true,
                navigation: {
                    nextEl: ".swiper-button-next",
                    prevEl: ".swiper-button-prev",
                },
            });
        </script>


        <script>
            var swiper = new Swiper(".mySwiper", {
                slidesPerView: 3,
                spaceBetween: 10,
                freeMode: true,
                loop: true,
                navigation: {
                    nextEl: ".swiper-button-next",
                    prevEl: ".swiper-button-prev",
                },
            });
        </script>

        <script>
            var swiper = new Swiper(".mySwiper3", {
                slidesPerView: 5,
                spaceBetween: 20,
                freeMode: true,
                loop: true,
                navigation: {
                    nextEl: ".swiper-button-next",
                    prevEl: ".swiper-button-prev",
                },
            });
        </script>

        <script>
            var swiper = new Swiper(".mySwiper2", {
                slidesPerView: 14,
                spaceBetween: 20,
                // freeMode: true,
                loop: true,
                navigation: {
                    nextEl: ".swiper-button-next",
                    prevEl: ".swiper-button-prev",
                },
            });
        </script>

        <script>
            function showMoreImages() {
                const hidden = document.querySelectorAll('.hidden-image');
                hidden.forEach(el => el.style.display = 'block');
                document.querySelector('.overlay').parentElement.remove();
            }
        </script>

        <script>
            function getDirections(lat, lng) {
                if (lat && lng) {
                    // Open Google Maps with directions
                    const directionsUrl = `https://www.google.com/maps/dir/?api=1&destination=${lat},${lng}&travelmode=driving`;
                    window.open(directionsUrl, '_blank');
                } else {
                    alert('Location coordinates not available for this professional.');
                }
            }
        </script>
    @endpush

    @if ($user->profile && $user->profile->lat && $user->profile->lng)
        @include('layouts.includes.google-map', [
            'lat' => $user->profile->lat,
            'lng' => $user->profile->lng,
        ])
    @endif

    @push('js')
        <script>
            $(document).ready(function() {
                // Handle category tab clicks
                $('.category-tab-btn').on('click', function() {
                    var categoryId = $(this).data('category-id');

                    // Remove active class from all category tabs (including All tab)
                    $('#all-services-tab, .category-tab-btn').removeClass('active').attr('aria-selected',
                        'false');
                    // Add active class to clicked tab
                    $(this).addClass('active').attr('aria-selected', 'true');

                    // Hide all tab panes
                    $('#pills-tabContent .tab-pane').removeClass('show active');

                    // Show the selected category tab pane
                    $('#category-' + categoryId).addClass('show active');

                    // Activate the first subcategory tab within this category
                    var firstSubcategoryTab = $('#category-' + categoryId + ' .nav-pills .nav-link').first();
                    var firstSubcategoryPane = $('#category-' + categoryId + ' .tab-content .tab-pane').first();

                    // Remove active from all subcategory tabs in this category
                    $('#category-' + categoryId + ' .nav-pills .nav-link').removeClass('active').attr(
                        'aria-selected', 'false');
                    $('#category-' + categoryId + ' .tab-content .tab-pane').removeClass('show active');

                    // Activate first subcategory
                    firstSubcategoryTab.addClass('active').attr('aria-selected', 'true');
                    firstSubcategoryPane.addClass('show active');
                });

                // Handle All tab click
                $('#all-services-tab').on('click', function() {
                    // Remove active class from all category tabs
                    $('.category-tab-btn').removeClass('active').attr('aria-selected', 'false');
                    // Add active class to All tab
                    $(this).addClass('active').attr('aria-selected', 'true');

                    // Hide all tab panes
                    $('#pills-tabContent .tab-pane').removeClass('show active');
                    // Show All services tab pane
                    $('#all-services').addClass('show active');
                });

            });
        </script>
        <script>
            $(document).ready(function() {
                $('.main-heading-icon').click(function() {
                    var userId = $(this).data('user-id');
                    var heartIcon = $('#heart-icon');

                    $.ajax({
                        url: '{{ route('favorite_professionals') }}',
                        type: 'POST',
                        data: {
                            _token: '{{ csrf_token() }}',
                            professional: userId
                        },
                        success: function(response) {
                            if (response.status === 'success') {
                                if (response.action === 'added') {
                                    // Change to filled red heart
                                    heartIcon.removeClass('far').addClass('fas text-danger');
                                } else if (response.action === 'removed') {
                                    // Change to outline heart
                                    heartIcon.removeClass('fas text-danger').addClass('far');
                                }
                            }
                        },
                        error: function(xhr, status, error) {
                            Swal.fire({
                                icon: 'error',
                                title: 'Error!',
                                text: 'Something went wrong. Please try again.',
                                showConfirmButton: true
                            });
                        }
                    });
                });

                // Handle Generate/Copy Short URL button
                $('#generateShortUrlBtn').on('click', function() {
                    const userId = $(this).data('user-id');
                    const $btn = $(this);
                    const originalText = $btn.html();

                    // Show loading state
                    $btn.prop('disabled', true).html(
                        '<span class="spinner-border spinner-border-sm me-1"></span>Loading...');

                    $.ajax({
                        url: '{{ route('generate_short_url') }}',
                        method: 'POST',
                        data: {
                            _token: '{{ csrf_token() }}',
                            user_id: userId
                        },
                        success: function(response) {
                            if (response.success) {
                                // Copy to clipboard
                                navigator.clipboard.writeText(response.short_url).then(function() {
                                    // Show success message
                                    Swal.fire({
                                        icon: 'success',
                                        title: 'Success!',
                                        text: 'Short URL copied to clipboard!',
                                        timer: 2000,
                                        showConfirmButton: false
                                    });

                                    // Update button text
                                    $btn.html(
                                        '<i class="bi bi-link-45deg me-1"></i>Copy Short URL'
                                        );
                                }).catch(function() {
                                    // Fallback: show URL in alert if clipboard fails
                                    Swal.fire({
                                        icon: 'success',
                                        title: 'Short URL Generated!',
                                        html: `Your short URL: <br><strong>${response.short_url}</strong><br><small>Please copy it manually</small>`,
                                        showConfirmButton: true
                                    });

                                    // Update button text
                                    $btn.html(
                                        '<i class="bi bi-link-45deg me-1"></i>Copy Short URL'
                                        );
                                });
                            } else {
                                Swal.fire({
                                    icon: 'error',
                                    title: 'Error!',
                                    text: response.message ||
                                        'Failed to generate short URL',
                                    showConfirmButton: true
                                });
                            }
                        },
                        error: function(xhr) {
                            let errorMessage = 'An error occurred';
                            if (xhr.responseJSON && xhr.responseJSON.message) {
                                errorMessage = xhr.responseJSON.message;
                            }

                            Swal.fire({
                                icon: 'error',
                                title: 'Error!',
                                text: errorMessage,
                                showConfirmButton: true
                            });
                        },
                        complete: function() {
                            // Reset button state
                            $btn.prop('disabled', false);
                            if ($btn.html().includes('Loading')) {
                                $btn.html(originalText);
                            }
                        }
                    });
                });
            });
        </script>
    @endpush
