  @if (Auth::user()->hasAnyRole(['individual', 'business']))
      <div class="mb-5">
          <div class="col-md-12">
              <label for="clientName" class="form-label form-input-labels">Client Name</label>
              <input type="text" class="form-control form-inputs-field" name="clientName" id="clientName"
                  placeholder="Enter client name">
          </div>
          <div class="col-md-12">
              <label for="clientEmail" class="form-label form-input-labels">Email</label>
              <input type="email" class="form-control form-inputs-field" name="clientEmail" id="clientEmail"
                  placeholder="Enter client email">

          </div>
          <div class="col-md-12">
              <label for="phone" class="form-label form-input-label">Phone Number</label>
              <div class="form-control form-inputs-field">
                  <input type="tel" id="phone" name="phone" placeholder="Enter phone number">
              </div>
          </div>
          <div class="col-md-12">
              <!-- service name -->
              <!-- <div class="service-details p-3"> -->
              <label for="service" class="form-label form-input-labels">Select Service</label>
              <select class="form-select form-select-field" id="service" name="service" data-control="select2"
                  data-placeholder="Select">
                  <option disabled selected>Select Service</option>
                  @foreach (auth()->user()->services as $service)
                      <option value="{{ $service->id }}" data-price="${{ number_format($service->price, 2) }}"
                          data-duration="{{ $service->duration }}">
                          {{ $service->name }}
                      </option>
                  @endforeach
              </select>
              <!-- </div> -->
          </div>
      </div>
  @endif
  <form class="form-add-services">
      <div class="row row-gap-5">
          <div class="col-md-12">
              <!-- service name -->
              <div class="service-details d-flex justify-content-between p-3">
                  <div class="service-name">
                      <input type="hidden" name="service_id" value="{{ $service->ids ?? '' }}">
                      <label class="form-label form-input-labels">{{ $service->name ?? '' }}</label>
                      <p class="fs-14 normal light-black">{{ $service->duration ?? '' }} mins</p>
                  </div>
                  <div class="service-price">
                      <p class="fs-14 normal light-black">${{ $service->price ?? '' }}</p>
                  </div>
              </div>

          </div>
          <!-- service-availed -->
          @if (Auth::user()->hasRole('customer'))
              <div class="col-md-12">
                  <div class="service-details d-flex justify-content-between p-3">
                      <div class="service-name w-100">
                          <label class="form-label form-input-labels pb-3">Service Availed by</label>
                          <div class="checkbox-service">
                              <label class="category-checkbox mb-3">
                                  <input class="family_category" type="radio" name="category" value="myself" checked>
                                  <p class="d-flex flex-column">Booking for myself <span class="span-class-service">I
                                          want to book this service for myself.</span></p>

                              </label>
                              @if (auth()->check() && auth()->user()->friends->isNotEmpty())
                                  <label class="category-checkbox">
                                      <input class="family_category" type="radio" name="category" value="family">
                                      <p class="d-flex flex-column">
                                          Booking for my friend/family
                                          <span class="span-class-service">
                                              I want to book this on behalf of my friend.
                                          </span>
                                      </p>
                                  </label>
                              @endif

                          </div>
                      </div>
                  </div>
                  <div id="service_availability" class="col-md-12">

                  </div>
              </div>
          @endif
          <!-- select professional -->
          @if ($service->user->hasRole('business'))
              <div class="col-md-12">
                  <div class="service-details d-flex justify-content-between p-3 pb-5">
                      <div class="service-name w-100">
                          <label class="form-label form-input-labels pb-3">Select Professional <span
                                  class="text-danger">*</span></label>
                          <div class="professional-checkbox row row-gap-5" id="professional-selection">
                              @forelse ($service->staff as $professional)
                                  <div class="col-md-4">
                                      <label class="category-checkbox professional-option"
                                          data-professional-id="{{ $professional->id }}"
                                          data-professional-name="{{ $professional->name }}">
                                          <input type="checkbox" name="professionals[]" value="{{ $professional->id }}"
                                              class="professional-checkbox-input">
                                          <div class="d-flex flex-column align-items-center">
                                              <div
                                                  class="card-header border-0 p-0 position-relative justify-content-center align-items-center">
                                                  <img onerror="this.src='{{ asset('website/assets/images/default.png') }}'"
                                                      src="{{ asset('website' . $professional->image) }}"
                                                      class="h-90px w-90px object-fit-contain rounded-pill top-rated-image"
                                                      alt="card-image">
                                              </div>
                                              <div class="card-body p-0">
                                                  <p
                                                      class="professional-name fs-16 semi_bold black text-center m-0 w-100 shadow-none">
                                                      {{ $professional->name }}
                                                  </p>
                                                  @if ($professional->email)
                                                      <p
                                                          class="professional-email fs-12 regular light-black opacity-6 text-center m-0 w-100 shadow-none">
                                                          {{ $professional->email }}
                                                      </p>
                                                  @endif
                                              </div>
                                          </div>
                                      </label>
                                  </div>
                              @empty
                                  <p class="fs-14 sora light-black normal service-details">No Professionals Found</p>
                              @endforelse
                          </div>
                      </div>
                  </div>
              </div>
          @endif
          <!-- date nd time -->
          <div class="col-md-12">
              <div class="calendar-container">
                  <input type="hidden" name="booking_date" value="">
                  <input type="hidden" name="booking_time" value="">
                  <input type="hidden" name="booking_number" value="">
                  <div class="title">Select Date & Time</div>

                  <div class="date-header">
                      <select class="month-select" id="monthSelect">
                          <!-- Options will be added by JavaScript -->
                      </select>
                      <div class="nav-buttons">
                          <button type="button" class="nav-btn" id="prevBtn">‹</button>
                          <button type="button" class="nav-btn" id="nextBtn">›</button>
                      </div>
                  </div>

                  <div class="date-grid" id="dateGrid">
                      <!-- Dates will be added by JavaScript -->
                  </div>


                  <div class="time-section-title mt-8 mb-3">Available Time Slots</div>
                  <div class="time-grid mb-8" id="timeGrid">
                      <!-- Time slots will be added by JavaScript -->
                  </div>

                  <div class="selection-display" id="selectionDisplay">
                      <div class="no-selection">Select a date and time</div>
                  </div>
              </div>
              <!-- <div class="calendar service-details p-3 pb-5">
                                <label class="form-label form-input-labels pb-3">Select Date & Time</label>
                                <div class="date-header">
                                    <div>
                                        <select id="monthSelect" class="border-0 fs-14 semi-bold black outline-0"
                                            onchange="loadDays()"></select>
                                    </div>
                                    <div>
                                        <button type="button" onclick="changeWeek(-1)"><i
                                                class="fa-solid fa-chevron-left"></i></button>
                                        <button type="button" onclick="changeWeek(1)"><i
                                                class="fa-solid fa-chevron-right"></i></button>
                                    </div>
                                </div>
                                <label class="category-checkbox">
                                    <input type="radio" name="category" value="date-days" checked>
                                    <div id="dateDays"
                                        class="date-days d-flex gap-2 justify-content-center flex-nowrap overflow-auto">
                                    </div>
                                </label>
                                <label class="form-label form-input-labels py-5">Available Time Slots</label>
                                <label class="category-checkbox">
                                    <input type="radio" name="category" value="time slots" checked>
                                    <div class="row">
                                        <div id="timeSlots"
                                            class="time-slots d-flex justify-content-between flex-wrap gap-3"></div>
                                    </div>
                                </label>
                            </div> -->
          </div>
          <!-- location -->
          <div class="col-md-12">
              <div class="service-details d-flex justify-content-between p-3 pb-5">
                  <div class="service-name w-100">
                      <label class="form-label form-input-labels pb-3">Location</label>
                      <div class="row row-gap-5">
                          <div class="col-md-12 d-flex gap-4">
                              <label>
                                  <input checked class="form-check-input" type="radio" name="loc"
                                      value="provider">
                                  <span>Provider's location</span>

                              </label>
                              <label class="d-flex gap-2 align-items-center">
                                  <input class="form-check-input" type="radio" name="loc" value="home">
                                  <span>Your Home/Office</span>
                              </label>

                          </div>
                          <div>
                              <input type="hidden" name="lat" value="{{ $service->lat ?? '' }}" id="latitude"
                                  data-service-lat="{{ $service->lat ?? '' }}">
                              <input type="hidden" name="lng" value="{{ $service->lng ?? '' }}" id="longitude"
                                  data-service-lng="{{ $service->lng ?? '' }}">

                              <div class="custom_loc">
                                  <div id="map" style="height: 300px"></div>
                              </div>
                              <div class="mb-3">
                                  <label for="pac-input" class="form-label">Address</label>
                                  <input type="text" id="pac-input" class="form-control"
                                      placeholder="Search for a location">
                              </div>
                          </div>
                          <!-- Divs to show/hide -->
                          {{-- <div class="row row-gap-5" id="providers-loc" style="display: none;">
                              <div class="col-md-12">
                                  <label for="home-address" class="form-label">Address</label>
                                  <input type="text" name="home-address" id="home-address"
                                      class="form-control form-input" placeholder="Enter your home/office address"
                                      value="appartment-02" disabled />
                              </div>
                          </div> --}}
                          <div class="row row-gap-5" id="home-loc" style="display: none;">
                              {{-- <div class="col-md-12">
                                  <label for="home-address" class="form-label">Address</label>
                                  <input type="text" name="home-address" id="home-address"
                                      class="form-control form-input" placeholder="Enter your home/office address" />

                              </div> --}}
                              <div class="col-md-12 ">
                                  <label class="styled-checkbox d-flex gap-3">
                                      <input type="checkbox" name="future-bookings" id="future-bookings">
                                      <span class="fs-14 light-black normal">Save Address for future
                                          bookings</span>
                                  </label>
                              </div>
                          </div>
                      </div>
                  </div>
              </div>
          </div>
          <!-- comments -->
          <div class="col-md-12">
              <div class="service-details d-flex justify-content-between p-3 pb-5">
                  <div class="service-name w-100">
                      <label class="form-label form-input-labels pb-3">Comments</label>
                      <textarea name="comments" class="form-control" rows="5" placeholder="Write Comments"></textarea>
                      <p class="form-label form-input-labels py-2 light-black">Comment for any special
                          instructions.</p>
                  </div>
              </div>
          </div>
      </div>
  </form>
