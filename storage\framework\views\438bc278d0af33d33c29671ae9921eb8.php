<?php $__empty_1 = true; $__currentLoopData = $bookings; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $booking): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
    <tr>
        <td data-label="Booking ID"><?php echo e($booking->booking_number); ?></td>
        <td data-label="Customer Name"><?php echo e($booking->customer->name ?? '-'); ?></td>
        <td data-label="Service Name"><?php echo e($booking->service->name); ?></td>
        <td data-label="Service Type">
            <?php if($booking->service->category): ?>
                <?php echo e($booking->service->category->name); ?>

            <?php else: ?>
                -
            <?php endif; ?>
        </td>
        <?php if(auth()->check() && auth()->user()->hasRole('admin')): ?>
            <td data-label="Provider"><?php echo e($booking->service->user->name ?? '-'); ?></td>
        <?php endif; ?>
        <?php if($booking->status == 0): ?>
            <?php if($booking->booking_date > now()->toDateString()): ?>
                <td data-label="Status" class="status upcoming-status">Upcoming</td>
            <?php else: ?>
                <td data-label="Status" class="status ongoing-status">Ongoing</td>
            <?php endif; ?>
        <?php elseif($booking->status == 1): ?>
            <td data-label="Status" class="status paid-status">Completed</td>
        <?php elseif($booking->status == 2): ?>
            <td data-label="Status" class="status unpaid-status">Cancelled</td>
        <?php else: ?>
            <td data-label="Status" class="status">-</td>
        <?php endif; ?>
        <td data-label="Date & Time">
            <?php echo e(Carbon\Carbon::parse($booking->booking_date)->format('l, d M Y')); ?>

            -
            <?php echo e(Carbon\Carbon::parse($booking->booking_time)->format('h:i A')); ?>

        </td>
        <td data-label="Amount">$<?php echo e($booking->price ?? $booking->total_amount ?? 0); ?></td>
        <td data-label="Action">
            <div class="dropdown">
                <button class="drop-btn" type="button" id="dropdownMenuButton" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="bi bi-three-dots-vertical"></i>
                </button>
                <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                    <li>
                        <a class="dropdown-item view fs-14 regular"
                           href="<?php echo e(route('booking.detail', ["booking_number" => $booking->booking_number, "ids" => $booking->ids])); ?>">
                            <i class="bi bi-eye view-icon"></i>
                            View Details
                        </a>
                    </li>
                    <?php if($booking->status == 0): ?>
                        <?php if($booking->hasTimePassed()): ?>
                            <li>
                                <button class="dropdown-item complete fs-14 regular booking-action"
                                        type="button"
                                        data-booking-id="<?php echo e($booking->id); ?>"
                                        data-action="complete">
                                    <i class="bi bi-check-circle complete-icon"></i>
                                    Mark as Complete
                                </button>
                            </li>
                        <?php endif; ?>
                        <li>
                            <button class="dropdown-item cancel fs-14 regular booking-action"
                                    type="button"
                                    data-booking-id="<?php echo e($booking->id); ?>"
                                    data-action="cancel">
                                <i class="fa-solid fa-xmark cancel-icon"></i>
                                Cancel
                            </button>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </td>
    </tr>
<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
    <tr>
        <td colspan="<?php echo e(auth()->check() && auth()->user()->hasRole('admin') ? '9' : '8'); ?>" class="text-center">No bookings found</td>
    </tr>
<?php endif; ?>
<?php /**PATH D:\SALMAN\git\anders\resources\views/dashboard/business/partials/booking-table-rows.blade.php ENDPATH**/ ?>